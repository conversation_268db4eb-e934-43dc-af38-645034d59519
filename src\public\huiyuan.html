<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>客户</title>
    <link rel="stylesheet" href="./css/css-comment.css" />
    <link rel="stylesheet" href="component/css/component.css" />
    <link rel="stylesheet" href="css/print.css" />
    <link rel="stylesheet" href="css/huiyuan.css" />
    <link rel="stylesheet" href="./vue/element/<EMAIL>" />
    <link rel="stylesheet" href="css/shouyingtai_kaidan.css" />
    <link rel="stylesheet" href="css/chongzhi.css" />
    <link
      rel="stylesheet"
      href="https://at.alicdn.com/t/font_1156348_lrijkzmtfh.css"
    />
  </head>
  <body>
    <div id="app" v-cloak>
      <!--主体-->
      <div class="main">
        <!--左侧导航条-->
        <div class="left">
          <div class="left_menu">
            <ul>
              <li
                v-for="(item, index) in leftMenu"
                :class="isActive == index ? 'addclass' : '' "
                @click="getmenu(index)"
              >
                {{item.word}}
              </li>
            </ul>
            <div
              class="f-left-nav-bg transition-all"
              :style="{top:isActive*60+40+'px'}"
            ></div>
          </div>
        </div>

        <div
          class="main-caontainer"
          v-cloak
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255,255, 0)"
        >
          <!--添加会员-->
          <div v-if="isActive==1" class="add_member_bg">
            <div class="huiyaun_add_huiyuan pt-10">
              <el-form
                :model="newMember"
                :rules="rules"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
              >
                <el-form-item label="客户昵称" prop="vip_v_name">
                  <el-input
                    v-model="newMember.vip_v_name"
                    placeholder="请输入姓名"
                    ref="vip_v_name"
                    class="member_input"
                  ></el-input>
                </el-form-item>
                <el-form-item label="备注名" prop="vip_v_dear_name">
                  <el-input
                    v-model="newMember.vip_v_dear_name"
                    placeholder="建议输入用户真实姓名"
                    class="member_input"
                  ></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="vip_v_tel">
                  <el-input
                    v-model="newMember.vip_v_tel"
                    placeholder="请输入手机号"
                    maxlength="11"
                    type="tel"
                    class="member_input"
                  ></el-input>
                </el-form-item>
                <el-form-item label="客户编号" prop="vip_v_number">
                  <el-input
                    v-model="newMember.vip_v_number"
                    placeholder="请输入客户编号"
                    class="member_input"
                  ></el-input>
                </el-form-item>
                <el-form-item label="客户来源" prop="vip_source_name">
                  <el-select
                    v-model="newMember.vip_source_name"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item,index) in vip_source_name_model"
                      :key="item.id"
                      :label="item.source_name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="身份" prop="is_vip">
                  <template>
                    <el-radio-group
                      v-model="newMember.is_vip"
                      @change="changeIdentity"
                    >
                      <el-radio :label="0">普通客户</el-radio>
                      <el-radio :label="1">会员客户</el-radio>
                    </el-radio-group>
                  </template>
                </el-form-item> -->
                <!-- <el-form-item label="会员等级" prop="vip_grade_name">
                  <el-select
                    v-model="newMember.vip_grade_name"
                    placeholder="请选择"
                    @change="bindChangeGrade"
                  >
                    <el-option
                      v-for="(item,index) in vip_grade_model_name"
                      :disabled="(newMember.is_vip==0 && item.growth_value>0) ||(newMember.is_vip==1 && item.growth_value==0 )"
                      :key="item.id"
                      :label="item.level_name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item label="性别" prop="vip_is_sex">
                  <el-button
                    class="vip_sex"
                    @click="chioce_nvxing"
                    size="small"
                    :type="newMember.vip_is_sex == 1 ?'primary':'none'"
                  >
                    男
                  </el-button>
                  <el-button
                    class="vip_sex"
                    @click="chioce_nanxing"
                    size="small"
                    :type="newMember.vip_is_sex == 2 ?'primary':'none'"
                  >
                    女
                  </el-button>
                </el-form-item>
                <el-form-item label="生日" prop="vip_v_ber_data">
                  <el-date-picker
                    v-model="newMember.vip_v_ber_data"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="微信号" prop="vip_v_weixin">
                  <el-input
                    v-model="newMember.vip_v_weixin"
                    placeholder="请输入"
                    class="member_input"
                  ></el-input>
                </el-form-item>
                <!-- <el-form-item label="新客归属" prop="curConsultant">
                  <el-select
                    v-model="newMember.curConsultant"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item,index) in marketingConsultant"
                      :key="item.id"
                      :label="item.nickname"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item label="客户备注" prop="vip_v_beizhu">
                  <el-input
                    v-model="newMember.vip_v_beizhu"
                    placeholder="请输入"
                    class="member_input"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="save_vip_info('ruleForm')">
                    完成
                  </el-button>
                  <el-button @click="qingkong_vip_info('ruleForm')">
                    清空
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!--查看会员-->
          <div v-if="isActive==0" style="width: 100%">
            <div class="hy_chakan_kaidan">
              <div class="hy_chakan_kaidan_font0">客户列表</div>
              <div class="hy_chakan_kaidan_font1" @click="hy_shaixuan">
                筛选
              </div>
            </div>
            <!--搜索-->
            <div class="hy_chakan_kandan_sousuo">
              <!--<el-input placeholder="请输入会员编号或手机" suffix-icon="el-icon-search"></el-input>-->
              <div class="el-input el-input--suffix">
                <input
                  type="text"
                  v-model.trim="vipSearch"
                  autocomplete="off"
                  placeholder="请输入客户编号、手机号、客户名称、备注名"
                  @keyup.enter.exact.stop="bindSearch"
                  class="el-input__inner"
                />
                <span class="el-input__suffix" @click="bindSearch">
                  <span class="el-input__suffix-inner">
                    <i
                      class="el-input__icon el-icon-search"
                      style="cursor: pointer"
                    ></i>
                  </span>
                </span>
              </div>
            </div>
            <!--分页-->
            <div class="hy_chakan_table">
              <el-table
                :data="vip_info"
                class="vipTable"
                @row-click="see_vip_details"
                ref="vipTable"
              >
                <el-table-column label="客户详情" min-width="280">
                  <template slot-scope="scope">
                    <div class="flex items-center space-x-2">
                      <div>
                        <img
                          v-if="scope.row.pic"
                          class="hy_info_touxiang"
                          :src="scope.row.pic"
                          alt=""
                        />
                        <img
                          v-else
                          class="hy_info_touxiang"
                          src="images/touxoang.png"
                          alt=""
                        />
                      </div>
                      <div>
                        <div class="flex space-x-2 items-center">
                          <div class="member_name font-bold">
                            {{scope.row.member_name}}
                          </div>
                          <el-tag
                            size="mini"
                            v-if="scope.row.sex == 1 || scope.row.sex == 2"
                            :type="scope.row.sex == 1 ? 'primary' : scope.row.sex == 2 ? 'danger' : 'info'"
                          >
                            {{scope.row.sex == 1 ? '男' :scope.row.sex == 2 ?
                            '女' : ''}}
                          </el-tag>
                          <el-tag
                            v-if="scope.row.is_vip"
                            size="mini"
                            type="warning"
                          >
                            会员
                          </el-tag>
                        </div>
                        <div class="text-sm">
                          <span class="text-gray">账号/手机：</span>
                          <span>{{scope.row.phone}}</span>
                          <!-- <span>&emsp;{{scope.row.growth_value}}</span> -->
                        </div>
                        <div class="text-sm">
                          <span class="text-gray">客户编号：</span>
                          <span v-if="scope.row.member_number!=0">
                            {{scope.row.member_number}}
                          </span>
                          <span v-else>无</span>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="累计消费" min-width="180">
                  <template slot-scope="scope">
                    <div class="text-sm pr-8">
                      <div class="flex justify-between items-baseline">
                        <div class="text-gray shrink-0">金额：</div>
                        <div class="flex-1 text-right font-bold text-normal">
                          {{ formatMoney(scope.row.total)}}
                        </div>
                      </div>
                      <div class="flex justify-between">
                        <div class="text-gray">次数：</div>
                        <div class="flex-1 text-right">
                          {{scope.row.count}}
                          <span class="text-gray">次</span>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="last_time"
                  label="距上次消费"
                  width="100"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.last_time">
                      {{calculateDaysAgo(scope.row.last_time)}}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="addtime"
                  label="创建时间"
                  min-width="130"
                >
                  <template slot-scope="scope">
                    <div class="text-sm">{{ scope.row.addtime}}</div>
                    <div class="text-sm">
                      <span class="text-gray">至今：</span>
                      {{ fromTheCurrentTime(scope.row.addtime) }}
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  prop="store_id"
                  label="归属门店"
                ></el-table-column> -->
                <!-- <el-table-column label="新客归属" label="adviser">
                  <template slot-scope="scope">
                    <span v-if="scope.row.adviser">{{scope.row.adviser}}</span>
                    <span v-if="!scope.row.adviser">--</span>
                  </template>
                </el-table-column> -->
                <el-table-column prop="birthday" label="生日/年龄" width="130">
                  <template slot-scope="scope">
                    <div class="text-sm">{{ scope.row.birthday}}</div>
                    <div class="text-sm">{{ getAge(scope.row.birthday) }}</div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="fengye" v-if="vip_info.length>0 || allCount>0">
                <el-pagination
                  background
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :page-size="limit"
                  :current-page="currentPage"
                  layout="total, prev, pager, next, jumper"
                  :total="allCount"
                ></el-pagination>
              </div>
            </div>
            <!--筛选弹框-->
            <el-dialog
              title="客户筛选"
              :visible.sync="is_hy_from"
              :close-on-click-modal="false"
              :close-on-press-escape="true"
              custom-class="details-mask"
              top="100px"
              width="60%"
              :show-close="false"
            >
              <div slot="title">
                <div class="dialog-title">
                  <div class="del_shaixuan" @click="del_filter">清除筛选</div>
                  <div class="shaixuan_titel">筛选</div>
                  <div class="shaixuan_wancheng" @click="filterCarryOut">
                    完成
                  </div>
                </div>
              </div>
              <div class="memberScreening" ref="memberScreening">
                <ul class="screening-list">
                  <li class="list-tabel">
                    <p class="list-tabel-title">消费频次</p>
                    <div class="hy_shaixuan1">
                      <div
                        v-for="(value,index) in rateArr"
                        class="hy_shaixuan1_font"
                        :class="rateIndex==index ? 'shaixuan1_add':'' "
                        @click="bindRate(index,value)"
                      >
                        {{value.word}}
                      </div>
                    </div>
                    <div style="display: flex; align-items: center">
                      <div class="element-input">
                        <el-input
                          size="small"
                          placeholder="自定义"
                          v-model.trim="rateCustomize"
                          class="hy_shaixuan1_input"
                        ></el-input>
                      </div>
                      <span class="screening-label">月未消费</span>
                    </div>
                  </li>
                  <li class="list-tabel">
                    <p class="list-tabel-title">消费次数</p>
                    <div class="hy_shaixuan1">
                      <div
                        v-for="(value,index) in frequencyArr"
                        class="hy_shaixuan1_font"
                        :class="frequencyIndex==index ? 'shaixuan1_add':'' "
                        @click="bindFrequency(index,value)"
                      >
                        {{value.word}}
                      </div>
                    </div>
                    <div style="display: flex; align-items: center">
                      <div class="element-input">
                        <el-input
                          placeholder="自定义"
                          size="small"
                          v-model.trim="frequencyCustomize"
                          class="hy_shaixuan1_input"
                        ></el-input>
                      </div>
                      <span>次以内</span>
                    </div>
                  </li>
                  <li class="list-tabel">
                    <p class="list-tabel-title">生日时间</p>
                    <div class="hy_shaixuan1">
                      <div
                        v-for="(value,index) in birthdayArr"
                        class="hy_shaixuan1_font"
                        :class="birthdayIndex==index ? 'shaixuan1_add':'' "
                        @click="bindBirthday(index,value)"
                      >
                        {{value.word}}
                      </div>
                    </div>
                    <div style="display: flex">
                      <div class="block">
                        <el-date-picker
                          v-model.trim="birthdayInput"
                          type="daterange"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                        ></el-date-picker>
                      </div>
                    </div>
                  </li>
                </ul>

                <!-- <el-collapse v-model="activeNames" @change="handleChange">
                  <el-collapse-item title="会员积分" name="1">
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;
                      "
                    >
                      <el-input
                        placeholder="自定义"
                        size="small"
                        v-model="memberPointsMin"
                        class="hy_shaixuan1_input"
                      ></el-input>
                      <span style="margin: 0 15px">至</span>
                      <el-input
                        placeholder="自定义"
                        size="small"
                        v-model="memberPointsMax"
                        class="hy_shaixuan1_input"
                      ></el-input>
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="客户等级" name="5">
                    <div class="hy_shaixuan1">
                      <div
                        v-for="(value,index) in vip_grade_model_name"
                        class="hy_shaixuan1_font"
                        :class="vip_gradeIndex==index ? 'shaixuan1_add':'' "
                        @click="bindVip_grade(index,value)"
                      >
                        {{value.level_name}}
                      </div>
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="客户来源" name="6">
                    <div class="hy_shaixuan1">
                      <div
                        v-for="(value,index) in vip_source_name_model"
                        class="hy_shaixuan1_font"
                        :class="vip_sourceIndex==index ? 'shaixuan1_add':'' "
                        @click="bindVip_source(index,value)"
                      >
                        {{value.source_name}}
                      </div>
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="新客归属" name="7">
                    <div class="hy_shaixuan1">
                      <div
                        v-for="(value,index) in marketingConsultant"
                        class="hy_shaixuan1_font"
                        :class="consultantIndex==index ? 'shaixuan1_add':'' "
                        @click="bindConsultant(index,value)"
                      >
                        {{value.nickname}}
                      </div>
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="会员标签" name="8">
                    <div class="hy_shaixuan1">
                      <div
                        v-for="(value,index) in vip_tabel_tag"
                        class="hy_shaixuan1_font"
                        :class="tabIndex==index ? 'shaixuan1_add':'' "
                        @click="bindTab(index,value)"
                      >
                        {{value.tag_name}}
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse> -->
              </div>
            </el-dialog>
            <!--查看会员详情弹框编辑-->
            <el-dialog
              title="客户详情"
              top="0px"
              :show-close="is_chacha1"
              :visible.sync="hy_vip_detail_model"
              :fullscreen="isfullscreen1"
            >
              <template slot="title">
                <div class="hy_model1_title">
                  <!-- <div
                    class="iconfont iconleft-arrow"
                    @click="back_chakan_vip"
                  ></div> -->
                  <p class="return-text" @click="back_chakan_vip">
                    <i class="el-icon-arrow-left"></i>
                    <span>返回</span>
                  </p>
                  <div style="margin: auto">客户详情</div>
                  <!--<div @click="bianji_jiben_xinxi">编辑基本信息</div>-->
                </div>
              </template>
              <el-scrollbar
                class="my-scrollbar-y"
                style="height: calc(100vh - 70px)"
              >
                <div style="width: 96%; margin: auto">
                  <div class="w-full flex space-x-4">
                    <div class="flex items-center">
                      <img
                        class="vip_model1_imgs"
                        :src="vipDetails.pic ==''? './images/touxoang.png':vipDetails.pic"
                        alt="头像"
                      />
                    </div>
                    <div class="flex-1">
                      <div class="flex gap-3 items-center mb-2">
                        <div class="text-2xl font-bold">
                          {{vipDetails.member_name}}
                        </div>
                        <div class="mt-1">{{vipDetails.remarks_name}}</div>
                        <el-tag
                          size="small"
                          v-if="[1,2].includes(vipDetails.sex)"
                          :type="vipDetails.sex == 1 ? 'primary' : vipDetails.sex == 2 ? 'danger' : 'info'"
                        >
                          {{vipDetails.sex == 1 ? '男' : vipDetails.sex == 2 ?
                          '女' : '未知'}}
                        </el-tag>
                        <div class="ml-auto">
                          <el-popover
                            @after-enter="changePass"
                            placement="right"
                            width="320"
                            trigger="click"
                          >
                            <div>
                              <p style="text-align: center; margin-top: 16px">
                                扫一扫，在手机上修改密码
                              </p>
                              <div style="text-align: center">
                                <div
                                  style="
                                    width: 240px;
                                    height: 240px;
                                    margin: 10px auto;
                                  "
                                  ref="changePass"
                                ></div>
                              </div>
                            </div>
                            <el-button size="mini" slot="reference">
                              修改密码
                            </el-button>
                          </el-popover>
                        </div>
                      </div>
                      <div class="flex space-x-12">
                        <div>
                          <div class="f_vip_detail_title">
                            <div>是否会员：</div>
                            <el-tag
                              size="small"
                              :type="vipDetails.is_vip == 1 ? 'warning' : 'info'"
                            >
                              {{vipDetails.is_vip == 1 ? '会员' : '非会员'}}
                            </el-tag>
                          </div>
                          <div class="f_vip_detail_title">
                            <div>会员职称：</div>
                            <div>{{vipDetails.jobName}}</div>
                          </div>
                        </div>
                        <div>
                          <div class="f_vip_detail_title">
                            <div>客户编号：</div>
                            <div>{{vipDetails.member_number}}</div>
                          </div>
                          <div class="f_vip_detail_title">
                            <div>账号/手机：</div>
                            <div>{{vipDetails.phone}}</div>
                          </div>
                        </div>
                        <div class="flex-1">
                          <div class="f_vip_detail_title">
                            <div>创建日期：</div>
                            <div>{{vipDetails.addtime}}</div>
                          </div>
                          <div class="f_vip_detail_title">
                            <div>客户来源：</div>
                            <div>{{vipDetails.member_source}}</div>
                          </div>
                        </div>
                        <!-- <div class="flex-1">
                          <div class="f_vip_detail_title">
                            <div>归属门店：</div>
                            <div>{{vipDetails.store_id}}</div>
                          </div>
                          <div class="f_vip_detail_title">
                            <div>新客归属：</div>
                            <div>{{vipDetails.adviser}}</div>
                          </div>
                        </div> -->
                        <div>
                          <el-button
                            v-for="(item,index) in vip_info_anniu1"
                            type="primary"
                            @click="vip_details_chioce_ycbk(item.keyWord)"
                          >
                            {{item.word}}
                          </el-button>
                          <!-- <el-dropdown>
                            <el-button type="primary" size="small">
                              业务
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <template v-for="(item,index) in vip_info_anniu1">
                                <el-dropdown-item
                                  @click.native="vip_details_chioce_ycbk(item.keyWord)"
                                >
                                  {{item.word}}
                                </el-dropdown-item>
                              </template>
                            </el-dropdown-menu>
                          </el-dropdown> -->
                          <!-- <el-dropdown>
                            <el-button
                              type="primary"
                              size="small"
                              style="margin-left: 10px"
                            >
                              实体卡
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <template
                                v-for="(value,index) in vip_info_anniu2"
                              >
                                <el-dropdown-item
                                  @click.native="model1_go_fufei_vip(index)"
                                >
                                  {{value.ka1}}
                                </el-dropdown-item>
                              </template>
                            </el-dropdown-menu>
                          </el-dropdown> -->
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="vip_model1_info5_font1">
                    <!-- <div
                      class="vip_model1_info5_font10"
                      @click="memberBalance"
                      style="cursor: pointer"
                    >
                      <div class="vip_model1_info5_font11">会员余额</div>
                      <div
                        class="vip_model1_info5_font5"
                        v-if="vipDetailsObj.vip_balance"
                      >
                        {{vipDetailsObj.vip_balance}}
                      </div>
                      <div class="vip_model1_info5_font7">
                        <div style="font-size: 10px; margin-bottom: 3px">
                          默认账户:&emsp;
                          <span style="color: #2b282c">
                            {{vipDetailsObj.balance}}
                          </span>
                        </div>
                        <div style="font-size: 10px">
                          充值卡:&emsp;
                          <span style="color: #2b282c">
                            {{vipDetailsObj.card_balance}}
                          </span>
                        </div>
                      </div>
                    </div> -->
                    <div class="vip_model1_info5_font10">
                      <div class="vip_model1_info5_font11">累计消费总额</div>
                      <div
                        class="vip_model1_info5_font12"
                        v-if="vipDetailsObj.total"
                      >
                        {{vipDetailsObj.total}}
                      </div>
                    </div>
                    <div class="vip_model1_info5_font10">
                      <div class="vip_model1_info5_font11">消费次数</div>
                      <div class="vip_model1_info5_font12" v-if="vipDetailsObj">
                        {{vipDetailsObj.count}}
                      </div>
                    </div>
                    <div
                      class="vip_model1_info5_font10"
                      @click="vip_model1_show_vipka"
                      style="cursor: pointer"
                    >
                      <div class="vip_model1_info5_font11">卡项</div>
                      <div
                        class="vip_model1_info5_font12 text-primary"
                        v-if="vipDetailsObj"
                      >
                        {{vipDetailsObj.card_num}}
                      </div>
                    </div>
                    <!-- <div
                      class="vip_model1_info5_font10"
                      @click="discountCoupon"
                      style="cursor: pointer"
                    >
                      <div class="vip_model1_info5_font11">优惠券</div>
                      <div class="vip_model1_info5_font12" v-if="vipDetailsObj">
                        {{vipDetailsObj.coupon_num}}
                      </div>
                    </div>
                    <div
                      class="vip_model1_info5_font10"
                      @click="accumulatePoints"
                      style="cursor: pointer"
                    >
                      <div class="vip_model1_info5_font11">可用积分</div>
                      <div class="vip_model1_info5_font12" v-if="vipDetailsObj">
                        {{vipDetailsObj.score}}
                      </div>
                    </div> -->

                    <div
                      class="vip_model1_info5_font10"
                      @click="memberBenfit"
                      style="cursor: pointer"
                    >
                      <div class="vip_model1_info5_font11">权益</div>
                      <div
                        class="vip_model1_info5_font12 text-primary"
                        v-if="vipDetailsObj"
                      >
                        {{vipDetailsObj.equity_num}}
                      </div>
                    </div>
                    <!-- <div
                      class="vip_model1_info5_font10"
                      @click="memberDebt"
                      style="cursor: pointer"
                    >
                      <div class="vip_model1_info5_font11">欠账</div>
                      <div class="vip_model1_info5_font12" v-if="vipDetailsObj">
                        {{vipDetailsObj.debt}}
                      </div>
                    </div> -->
                  </div>
                  <!-- <div class="vip_model1_info5_font1">
                    <div class="vip_model1_info5_font10">
                      <div class="vip_model1_info5_font11">消费次数</div>
                      <div class="vip_model1_info5_font12" v-if="vipDetailsObj">
                        {{vipDetailsObj.count}}
                      </div>
                    </div>
                    <div
                      class="vip_model1_info5_font10"
                      style="visibility: hidden"
                    >
                      <div class="vip_model1_info5_font11">消费次数</div>
                      <div class="vip_model1_info5_font12">
                        {{vip_model1_info[0].vip_xiaofeicishu}}
                      </div>
                    </div>
                    <div
                      class="vip_model1_info5_font10"
                      style="visibility: hidden"
                    >
                      <div class="vip_model1_info5_font11">消费次数</div>
                      <div class="vip_model1_info5_font12">
                        {{vip_model1_info[0].vip_xiaofeicishu}}
                      </div>
                    </div>
                    <div
                      class="vip_model1_info5_font10"
                      style="visibility: hidden"
                    >
                      <div class="vip_model1_info5_font11">消费次数</div>
                      <div class="vip_model1_info5_font12">
                        {{vip_model1_info[0].vip_xiaofeicishu}}
                      </div>
                    </div>
                    <div
                      class="vip_model1_info5_font10"
                      style="visibility: hidden"
                    >
                      <div class="vip_model1_info5_font11">消费次数</div>
                      <div class="vip_model1_info5_font12">
                        {{vip_model1_info[0].vip_xiaofeicishu}}
                      </div>
                    </div>
                  </div> -->
                  <div class="vip_model1_caozuo">
                    <el-tabs
                      v-model="vip_caozuo_Name"
                      @tab-click="vip_caozuo_Click"
                    >
                      <el-tab-pane label="客户信息" name="first">
                        <!-- <div class="vip_model1_vip_title_font1">
                          <div class="vip_model1_vip_title_font2">会员标签</div>
                          <div
                            class="vip_model1_vip_title_font3"
                            @click="vip_model1_addvip_title"
                          >
                            添加标签
                          </div>
                        </div>
                        <div class="vip_model1_vip_title_font5">
                          <div
                            v-for="value of vipDetailsObj.tab_id"
                            class="vip_model1_vip_title_font4"
                          >
                            {{value.tag_name}}
                          </div>
                          <div
                            v-if="vipDetailsObj && vipDetailsObj.tab_id &&vipDetailsObj.tab_id.length<=0"
                          >
                            暂无标签
                          </div>
                        </div> -->
                        <div class="vip_model1_vip_title_font6">
                          <div class="vip_model1_vip_title_font7">
                            <div class="vip_model1_vip_title_font8">
                              <div class="vip_model1_vip_title_font9">
                                基本档案
                              </div>
                              <div
                                class="vip_model1_vip_title_font10"
                                @click="hy_model6_add_dangan"
                              >
                                编辑
                              </div>
                            </div>
                            <div class="vip_model1_vip_title_font11">
                              <div class="vip_model1_vip_title_font12">
                                <div class="vip_model1_vip_title_font13">
                                  联系电话
                                </div>
                                <div class="vip_model1_vip_title_font13">
                                  微信
                                </div>
                                <div class="vip_model1_vip_title_font13">
                                  生日
                                </div>
                                <div class="vip_model1_vip_title_font13">
                                  详细地址
                                </div>
                                <div class="vip_model1_vip_title_font13">
                                  备注
                                </div>
                              </div>
                              <div
                                class="vip_model1_vip_title_font14"
                                v-if="vipDetailsObj"
                              >
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="vipDetailsObj.phone"
                                >
                                  {{vipDetailsObj.phone}}
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="!vipDetailsObj.phone"
                                >
                                  --
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="vipDetailsObj.wechat_number"
                                >
                                  {{vipDetailsObj.wechat_number}}
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="!vipDetailsObj.wechat_number"
                                >
                                  --
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="vipDetailsObj.birthday"
                                >
                                  {{vipDetailsObj.birthday}}，{{getAge(vipDetailsObj.birthday)}}
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="!vipDetailsObj.birthday"
                                >
                                  --
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="vipDetailsObj.address"
                                >
                                  {{vipDetailsObj.address}}
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="!vipDetailsObj.address"
                                >
                                  --
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="vipDetailsObj.remarks"
                                >
                                  {{vipDetailsObj.remarks}}
                                </div>
                                <div
                                  class="vip_model1_vip_title_font15"
                                  v-if="!vipDetailsObj.remarks"
                                >
                                  --
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="vip_model1_vip_title_font16" v-if="false">
                            <div class="vip_model1_vip_title_font17">
                              <div class="vip_model1_vip_title_font18">
                                私密档案
                              </div>
                              <div class="vip_model1_vip_title_font19">
                                编辑
                              </div>
                            </div>
                            <div class="vip_model1_vip_title_font20">
                              <div class="vip_model1_vip_title_font21">
                                <div class="vip_model1_vip_title_font22">
                                  私密档案内容
                                </div>
                                <div class="vip_model1_vip_title_font23">
                                  我的内容你不能看的
                                </div>
                              </div>
                              <div class="vip_model1_vip_title_font24">
                                <div class="vip_model1_vip_title_font25">
                                  图片
                                </div>
                                <div class="vip_model1_vip_title_font26">
                                  <img
                                    v-for="(value,index) in vip_model1_info[0].vip_model1_vip_imgsrl"
                                    :src="value.word"
                                    class="vip_model1_vip_title_font27"
                                    alt=""
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="交易记录" name="second">
                        <div class="trade_recond">
                          <el-table :data="tradeRecondData" style="width: 100%">
                            <el-table-column
                              label="订单号/开单时间"
                              min-width="280"
                            >
                              <template slot-scope="scope">
                                <div
                                  v-if="scope.row.order_number"
                                  class="text-primary hover:text-primary/80 cursor-pointer w-fit"
                                  @click.stop="tradeRecondRowClick(scope.row)"
                                >
                                  {{scope.row.order_number}}
                                </div>
                                <div
                                  v-if="scope.row.order_time"
                                  class="text-gray text-sm"
                                >
                                  {{scope.row.order_time}}
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column label="类型">
                              <template slot-scope="scope">
                                <div
                                  v-show="scope.row.type==1 || scope.row.type==2"
                                  class="o-tag-indigo o-tag"
                                >
                                  品项
                                </div>
                                <div
                                  v-show="scope.row.type==3"
                                  class="o-tag-fuchsia o-tag"
                                >
                                  售卡
                                </div>
                                <div v-show="scope.row.type==4">充值</div>
                                <div v-show="scope.row.type==5" class="o-tag-lime o-tag">充卡</div>
                                <div
                                  v-show="scope.row.type==6"
                                  class="o-tag-pink o-tag"
                                >
                                  直接收款
                                </div>
                              </template>
                            </el-table-column>
                            <!-- <el-table-column label="客户">
                              <template slot-scope="scope">
                                <p v-if="scope.row.vip">
                                  {{scope.row.vip.member_name}}
                                </p>
                                <p v-if="scope.row.vip">
                                  {{scope.row.vip.phone}}
                                </p>
                              </template>
                            </el-table-column> -->
                            <el-table-column label="商品" width="200">
                              <template
                                slot-scope="scope"
                                v-if="scope.row.orderInfo"
                              >
                                <p v-for="item of scope.row.orderInfo">
                                  <span>
                                    {{item.name}}{{item.sku_name?'：'+item.sku_name
                                    : ''}}
                                  </span>
                                </p>
                              </template>
                            </el-table-column>
                            <el-table-column label="">
                              <template slot="header" slot-scope="scope">
                                <technician-name></technician-name>
                              </template>
                              <template
                                slot-scope="scope"
                                v-if="scope.row.orderInfo"
                              >
                                <p v-for="item of scope.row.orderInfo">
                                  <span>{{item.Craftsman}}</span>
                                </p>
                              </template>
                            </el-table-column>
                            <el-table-column label="应收全款">
                              <template slot-scope="scope">
                                <span>￥{{scope.row.receivable}}</span>
                              </template>
                            </el-table-column>
                            <el-table-column prop="order_state" label="状态">
                              <template slot-scope="scope">
                                <el-tag
                                  size="medium"
                                  :type="getStateColor(scope.row.state)"
                                >
                                  {{scope.row.order_state}}
                                </el-tag>
                              </template>
                            </el-table-column>
                          </el-table>
                          <div
                            class="memberBalanceFengye"
                            v-if="tradeRecondData.length>0 || tradeRecondLength>0"
                          >
                            <el-pagination
                              background
                              @size-change="handleTradeRecondSizeChange"
                              @current-change="handleTradeRecondCurrentChange"
                              :page-size="tradeLimit"
                              :current-page="tradeCurrentPage"
                              layout="total, prev, pager, next, jumper"
                              :total="tradeRecondLength"
                            ></el-pagination>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="服务日志" name="third">
                        <div class="vip_model1_info_fuwu_hight">
                          <div
                            v-for="item of serviceLogData"
                            class="vip_model1_info_fuwu"
                          >
                            <div class="vip_model1_info_fuwu_font1">
                              <div class="vip_model1_info_fuwu_font2">
                                <div>
                                  <img
                                    :src="item.staffInfo.avatar"
                                    class="vip_model1_info_fuwu_font3"
                                    onerror="this.src='images/default.jpg'"
                                  />
                                </div>
                                <div class="vip_model1_info_fuwu_font4">
                                  <div class="vip_model1_info_fuwu_font5">
                                    {{item.staffInfo.nickname}}
                                    <span
                                      style="font-size: 14px; color: #528ccc"
                                      v-if="item.staffInfo.groupInfo"
                                    >
                                      {{item.staffInfo.groupInfo.groupName}}
                                    </span>
                                  </div>
                                  <div class="vip_model1_info_fuwu_font6">
                                    {{item.staffInfo.realname}}
                                  </div>
                                </div>
                              </div>
                              <div class="vip_model1_info_fuwu_font7">
                                {{item.addtime}}
                              </div>
                            </div>
                            <div class="service_log_content">
                              {{item.content}}
                            </div>
                            <div class="service_log_img">
                              <img
                                v-for="(imgs,index) in item.imgArr"
                                :key="index"
                                :src="imgs.file_path"
                                alt=""
                              />
                            </div>
                            <div class="vip_model1_info_fuwu_font8">
                              <span style="padding-left: 9px">关联服务:</span>
                              <el-table
                                :data="item.orderInfo"
                                :header-cell-style="vipClass"
                                style="width: 100%"
                              >
                                <el-table-column
                                  label="商品名称"
                                  prop="name"
                                  :show-overflow-tooltip="true"
                                ></el-table-column>
                                <el-table-column label="单价">
                                  <template slot-scope="scope">
                                    <span>￥{{scope.row.price}}</span>
                                  </template>
                                </el-table-column>
                                <el-table-column
                                  label="数量"
                                  prop="num"
                                ></el-table-column>
                                <el-table-column label="优惠权益" width="200">
                                  <template slot-scope="scope">
                                    <p v-if="scope.row.equity_type==1">
                                      <span>无权益</span>
                                    </p>
                                    <p v-if="scope.row.equity_type==2">
                                      <span>-￥{{scope.row.reduceprice}}</span>
                                      <span>折扣优惠</span>
                                    </p>
                                    <p v-if="scope.row.equity_type==3">
                                      <span>￥{{scope.row.reduceprice}}</span>
                                      <span>会员卡抵扣1次</span>
                                    </p>
                                    <p v-if="scope.row.equity_type==4">
                                      <span>￥{{scope.row.reduceprice}}</span>
                                      <span>优惠金额</span>
                                    </p>
                                  </template>
                                </el-table-column>
                                <el-table-column label="小计">
                                  <template slot-scope="scope">
                                    <span>￥{{scope.row.Subtotal}}</span>
                                  </template>
                                </el-table-column>
                              </el-table>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                </div>
              </el-scrollbar>
            </el-dialog>

            <!-- 点击会员编号模态框 -->
            <el-dialog
              top="0px"
              :show-close="true"
              width="600px"
              :visible.sync="isMemberShip"
              append-to-body
            >
              <div class="hy_model1_title_cz" style="margin-top: 7vh">
                <div class="hy_model1_cz_top1">会员实体卡</div>
                <!-- <div @click="isMemberShip = false"></div> -->
              </div>
              <div style="padding: 10px 40px 15px 40px">
                <template>
                  <el-table
                    :data="memberShipData"
                    class="memberboxStyle"
                    style="width: 100%"
                  >
                    <el-table-column
                      prop="card_num"
                      label="编号"
                      width="180"
                    ></el-table-column>
                    <el-table-column label="状态">
                      <template slot-scope="scope">
                        <span v-if="scope.row.status==1">使用中</span>
                        <span v-if="scope.row.status==2">禁用</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作">
                      <template slot-scope="scope">
                        <el-button
                          v-if="scope.row.status==1"
                          size="mini"
                          @click="changeEntityStatus(scope.$index, scope.row)"
                        >
                          禁用
                        </el-button>
                        <el-button
                          v-if="scope.row.status==2"
                          size="mini"
                          @click="changeEntityStatus(scope.$index, scope.row)"
                        >
                          恢复
                        </el-button>
                        <el-button
                          size="mini"
                          @click="untieCard(scope.$index, scope.row)"
                        >
                          解绑
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </div>
            </el-dialog>

            <!--会员详情页面的添加标签-->
            <el-dialog
              title="编辑基本信息"
              :visible.sync="is_Editing_vip_title"
              width="40%"
            >
              <template slot="title">
                <div class="hy_model1_Editing_info">
                  <div>添加客户标签</div>
                </div>
              </template>
              <div class="add_vip_title">
                <div class="add_vip_title1"></div>
                <div class="add_vip_title2_font1">自定义标签</div>
                <div class="add_vip_title2">
                  <div class="add_vip_title2_font2">
                    <input
                      class="add_vip_title2_font2_input"
                      type="text"
                      v-model="model5_title_input"
                      placeholder="请添加标签"
                    />
                  </div>
                  <div
                    class="add_vip_title2_font3"
                    @click="vip_add_title_to_titles"
                  >
                    添加
                  </div>
                </div>
                <div class="add_vip_title3">
                  <span class="add_vip_title3_font1">编辑客户标签</span>
                </div>
                <div class="add_vip_title4 space-x-3">
                  <el-tag
                    v-for="(value,index) in vip_tabel_tag"
                    @click="chooseMemeberTag(value)"
                    :type="value.isactive==1?'primary':'info'"
                    :effect="value.isactive==1?'dark':'plain'"
                    class="cursor-pointer"
                  >
                    {{value.tag_name}}
                  </el-tag>
                </div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="hy_model5_over_add_titel">取 消</el-button>
                <el-button type="primary" @click="hy_model5_save_add_titel">
                  确 定
                </el-button>
              </span>
            </el-dialog>

            <!--会员详情页面的编辑基本档案-->
            <el-dialog
              title="编辑基本档案"
              :visible.sync="is_Editing_vip_dangan"
              width="40%"
            >
              <template slot="title">
                <div class="hy_model1_Editing_info">
                  <div>编辑基本档案</div>
                </div>
              </template>
              <div class="hy_model1_Editing_dangan">
                <!--<div class="add_dangan_info1">-->
                <!--<div class="add_dangan_info1_font1">联系电话</div>-->
                <!--<div><input class="add_dangan_info1_font2" disabled type="text" v-model="vipDetailsObj.phone" placeholder="请输入"></div>-->
                <!--</div>-->
                <div class="add_dangan_info1">
                  <div class="add_dangan_info1_font1">微信号</div>
                  <div style="visibility: hidden">隐</div>
                  <div>
                    <input
                      class="add_dangan_info1_font2"
                      type="text"
                      v-model="vipDetailsObj.wechat_number
"
                      placeholder="请输入"
                    />
                  </div>
                </div>
                <div class="add_dangan_info1">
                  <div class="add_dangan_info1_font1">备注</div>
                  <div>
                    <input
                      class="add_dangan_info1_font2"
                      type="text"
                      v-model="vipDetailsObj.remarks"
                      placeholder="请输入"
                    />
                  </div>
                </div>
                <div class="add_dangan_info1">
                  <div class="add_dangan_info1_font1">生日</div>
                  <div style="visibility: hidden">隐藏</div>
                  <div style="margin-left: 20px">
                    <el-date-picker
                      v-model="vipDetailsObj.birthday"
                      type="date"
                      size="small"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </div>
                </div>
                <div class="add_dangan_info1">
                  <div class="add_dangan_info1_font1">地址</div>
                  <div style="visibility: hidden">隐藏</div>
                  <div>
                    <input
                      class="add_dangan_info1_font2"
                      type="text"
                      v-model="vipDetailsObj.address"
                      placeholder="请输入"
                    />
                  </div>
                </div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="hy_model6_over_add_dangan">取 消</el-button>
                <el-button type="primary" @click="hy_model6_save_add_dangan">
                  确 定
                </el-button>
              </span>
            </el-dialog>

            <!--会员详情里面的开通付费会员-->
            <el-dialog
              title="办理付费会员"
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :visible.sync="is_open_money_vip"
              append-to-body
            >
              <template slot="title">
                <div class="hy_model1_title">
                  <div
                    class="iconfont iconleft-arrow"
                    @click="back_vip_details_form_ff"
                  ></div>
                  <div>办理付费会员</div>
                  <div style="visibility: hidden">隐藏</div>
                </div>
              </template>
              <div class="main">
                <!--借鉴收银台充值页面-->
                <ul class="main-right">
                  <!---->
                  <li style="display: flex; width: 100%">
                    <!--选择订单内容-->
                    <div class="server">
                      <div class="vip_model2_left">
                        <div class="vip_model2_top">
                          <div class="vip_model2_top_font1">vip会员卡</div>
                          <div class="vip_model2_top_font2">专享会员权益</div>
                        </div>
                        <div class="vip_model2_mid">
                          <div class="vip_model2_mid1">
                            <div>
                              <img
                                class="vip_model2_mid1_font1"
                                src="./images/zhidingshangpin.png"
                                alt=""
                              />
                            </div>
                            <div class="vip_model2_mid1_font2">指定商品8折</div>
                          </div>
                          <div class="vip_model2_mid1">
                            <div>
                              <img
                                class="vip_model2_mid1_font1"
                                src="./images/huiyuanzhuanxiangjia.png"
                                alt=""
                              />
                            </div>
                            <div class="vip_model2_mid1_font2">会员专享价</div>
                          </div>
                        </div>
                        <div class="vip_model2_but">
                          <div class="vip_model2_mid1">
                            <div>
                              <img
                                class="vip_model2_mid1_font1"
                                src="./images/chaojihuiyuanri.png"
                                alt=""
                              />
                            </div>
                            <div class="vip_model2_mid1_font2">超级会员日</div>
                          </div>
                          <div class="vip_model2_mid1">
                            <div>
                              <img
                                class="vip_model2_mid1_font1"
                                src="./images/jifenjiabei.png"
                                alt=""
                              />
                            </div>
                            <div class="vip_model2_mid1_font2">积分加倍</div>
                          </div>
                        </div>
                        <div class="vip_model2_foot">
                          <div class="vip_model2_foot1">
                            <div>
                              <img
                                class="vip_model2_mid1_font1"
                                src="./images/zhuanshukefu.png"
                                alt=""
                              />
                            </div>
                            <div class="vip_model2_mid1_font2">专属客服</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!--分割线-->
                    <div class="menu_line"></div>
                    <!--开单内容-->
                    <div class="open_details">
                      <!--开单详情标题-->
                      <div class="open_details_border">
                        <div class="open_details_title">开通付费会员</div>
                        <div class="open_details_title_font2">清空页面</div>
                      </div>
                      <!--搜索夹内容和支付-->
                      <div>
                        <div class="cz_open_details_info">
                          <div style="" class="chonngzhi_vip_all">
                            <div class="chongzhi_vip">
                              <div class="chongzhi_vip_info">
                                <img
                                  class="chongzhi_vip_info_img"
                                  src="./images/zuhekatouxiang.jpg"
                                  alt="图片"
                                />
                              </div>
                              <div class="chongzhi_vip_info1">
                                <div class="chongzhi_vip_info_font1">
                                  {{vip_model2_info[0].vip_model2_name}}
                                </div>
                                <div class="chongzhi_vip_info_font2">
                                  <div>
                                    {{vip_model2_info[0].vip_model2_tel}}
                                  </div>
                                  <div>
                                    {{vip_model2_info[0].vip_model2_dengji}}
                                  </div>
                                </div>
                                <div class="chongzhi_vip_info_font3">
                                  <span>客户编号:&emsp;&emsp;</span>
                                  <span>
                                    {{vip_model2_info[0].vip_model2_num}}
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div
                              class="iconfont iconlajitong chongzhi_vip_info_font4"
                            ></div>
                          </div>
                          <div class="chongzhika_info">
                            <div class="hy_model2_fufeihuiyuan">
                              <div class="hy_model2_fufei_font1">
                                <div class="hy_model2_fufei_font2">
                                  <div class="hy_model2_fufei_font4">
                                    付费会员
                                  </div>
                                  <div class="hy_model2_fufei_font5">
                                    全店通用
                                  </div>
                                </div>
                                <div class="hy_model2_fufei_font3">
                                  <div class="hy_model2_fufei_font6">
                                    永久有效
                                  </div>
                                  <div
                                    class="iconfont iconlajitong hy_model2_fufei_font_lj"
                                  ></div>
                                </div>
                              </div>
                              <div class="hy_model2_fufei_font7">
                                <div
                                  v-for="(value,index) in vip_model2_info[0].vip_daiyu"
                                  class="hy_model2_fufei_font8"
                                >
                                  {{value.word}}
                                </div>
                              </div>
                            </div>
                            <div class="chongzhi_pay_num">
                              <div>
                                <span
                                  class="chioce_paynum_font1"
                                  @click="model2_youhui_quanyi"
                                >
                                  优惠权益&emsp;&emsp;&emsp;
                                </span>
                                <span
                                  class="chioce_paynum_font2"
                                  @click="model2_youhui_quanyi"
                                >
                                  请选择
                                </span>
                              </div>
                              <div>
                                <span></span>
                                <span class="iconfont iconarrow"></span>
                              </div>
                            </div>
                            <div class="vip_pay_cengson">
                              <span class="vip_pay_cengson_font1">
                                小计金额&emsp;&emsp;&emsp;
                              </span>
                              <span class="vip_pay_cengson_font2">￥{{}}</span>
                            </div>
                            <div class="vip_xuanze_xiaoshou">
                              <span
                                class="vip_xuanze_xiaoshou1"
                                @click="vip_model2_xuanxiao"
                              >
                                选择销售&emsp;&emsp;&emsp;
                              </span>
                              <span
                                class="vip_xuanze_xiaoshou2"
                                @click="vip_model2_xuanxiao"
                              >
                                请选择
                              </span>
                              <span
                                class="iconfont vip_xuanze_xiaoshou3"
                              ></span>
                            </div>
                            <div class="vip_add_zengson">
                              <span
                                class="add_zengson"
                                @click="vip_model2_add_zengson"
                              >
                                添加赠送
                              </span>
                              <span class="iconfont"></span>
                            </div>
                            <div class="vip-model2_server_name">
                              <div class="vip-model2_server_name_font1">
                                赠送服务项目
                              </div>
                              <div class="vip-model2_server_name_font2">
                                <div class="vip-model2_server_name_font3">
                                  美白嫩肤
                                </div>
                                <div class="hy_open_details_num">
                                  <span
                                    class="hy_span1"
                                    @click="hy_model2_jianshao"
                                  >
                                    -
                                  </span>
                                  <span class="hy_span2">
                                    {{vip_model2_info[0].hy_model2_server_time}}
                                  </span>
                                  <span
                                    class="hy_span3"
                                    @click="hy_model2_zengjia"
                                  >
                                    +
                                  </span>
                                  <span class="hy_span4">次</span>
                                </div>
                                <div class="vip-model2_server_name_font4">
                                  <span class="vip-model2_server_name_font5">
                                    {{vip_model2_info[0].vip_model2_youxiaoqi}}
                                  </span>
                                  <span class="vip-model2_server_name_font6">
                                    &emsp;前有效&emsp;
                                  </span>
                                  <span
                                    class="iconfont iconlajitong vip-model2_server_name_font7"
                                    @click="vip_model2_del_server"
                                  ></span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!--备注和支付-->
                        <div class="open_details_pay">
                          <div class="order_remark">
                            <p class="order_remark_font">订单备注</p>
                            <div class="order_remark_input">
                              <input
                                type="text"
                                placeholder="请输入"
                                class="remark_input"
                              />
                            </div>
                          </div>
                          <div style="background: #f1f1f1">
                            <div class="open_details_pay_choice">
                              <div class="open_details_pay_choice_font1">
                                <span>待支付&nbsp;&nbsp;</span>
                                <span>￥{{}}</span>
                              </div>
                              <div class="chongzhi_shoukuan">
                                <div class="chongzhi_shoukuan_font">收款</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>

                  <!---->
                </ul>
              </div>
            </el-dialog>

            <!--优惠权益-->
            <el-dialog
              title="选择可用的用户权益"
              :visible.sync="hy_model2_youhui_power"
              class="model2_quanyi"
              width="35%"
            >
              <template slot="title">
                <div class="hy_model2_user_qianyi">
                  <div>选择可用的用户权益</div>
                </div>
              </template>
              <div class="xuanze_qunayi_font0">不使用优惠券</div>
              <div class="xuanze_qunayi_font1">
                <div class="xuanze_qunayi_font2">优惠金额</div>
                <div class="xuanze_qunayi_font3">修改小计金额</div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="hy_model2_quxiao_quanyi">取 消</el-button>
                <el-button type="primary" @click="hy_model2_save_quanyi">
                  确 定
                </el-button>
              </span>
            </el-dialog>

            <!--选择销售模态框-->
            <el-dialog
              title="选择销售"
              :visible.sync="hy_model2_ischongxiao"
              width="35%"
            >
              <template slot="title">
                <div class="hy_model2_user_qianyi">
                  <div>选择销售</div>
                </div>
              </template>
              <div style="padding: 30px 20px">
                <div style="padding-bottom: 20px">
                  <el-input
                    placeholder="输入销售名称"
                    suffix-icon="el-icon-search"
                  ></el-input>
                </div>
                <div style="height: calc(100vh - 500px); overflow: auto">
                  <div
                    class="xuazne_xiaoshou"
                    v-for="(value , index) in vip_model2_info[0].hy_model2_xiaoshous "
                  >
                    <el-checkbox
                      v-model="hy_model2_check1"
                      style="height: 25px; width: 25px"
                    ></el-checkbox>
                    <span
                      style="cursor: pointer"
                      @click="hy_model2_chioce_xiaoshou(index)"
                    >
                      {{value.name}}
                    </span>
                  </div>
                </div>
                <div style="text-align: right">
                  <span slot="footer" class="dialog-footer">
                    <el-button @click="hy_model2_chongxiao_over">
                      取 消
                    </el-button>
                    <el-button type="primary" @click="hy_model2_xiaoshou_save">
                      确 定
                    </el-button>
                  </span>
                </div>
              </div>
            </el-dialog>

            <!--会员页面model2页面添加服务-->
            <el-dialog
              title="添加服务"
              :visible.sync="hy_model2_add_server"
              width="40%"
            >
              <template slot="title">
                <div class="hy_model2_user_qianyi">
                  <div>添加服务</div>
                </div>
              </template>
              <div style="padding: 30px 20px">
                <div>
                  <el-input
                    placeholder="请输入服务名称"
                    suffix-icon="el-icon-search"
                  ></el-input>
                </div>
                <div class="tianjia_fuwu_mian">
                  <div class="fuwu_biaoti">
                    <ul
                      v-for="(value,index) in vip_model2_info[0].hy_model2_server_biaoti"
                    >
                      <li
                        :class="hy_model2_ischong_Addfuwu == index ? 'tianjia_fuwu_font0' : 'tianjia_fuwu_font' "
                        @click="hy_model2_tianjia_fuwu(index)"
                      >
                        {{value.name}}
                      </li>
                    </ul>
                  </div>
                  <div class="fuwu_biaoti_line"></div>
                  <div class="fuwu_biaoti_chioce">
                    <ul
                      v-for="(value,index) in vip_model2_info[0].hy_model2_server_biaoti_name"
                      class="fuwu_biaoti_chioce_bottom"
                    >
                      <li class="server_biaoti_name_font">
                        <div>
                          <img
                            src="./images/yangtu.jpg"
                            alt="图片"
                            class="server_biaoti_name_font1"
                          />
                          <span class="server_biaoti_name_font2">
                            {{value.name}}
                          </span>
                        </div>
                        <div>
                          <span class="server_biaoti_name_font3">
                            ￥{{value.price}}
                          </span>
                          <el-checkbox
                            v-model="hy_model2_server_checked"
                            style="text-align: right"
                          ></el-checkbox>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="hy_model2_server_tianjia_over">
                  取 消
                </el-button>
                <el-button
                  type="primary"
                  @click="hy_model2_server_tianjia_save"
                >
                  确 定
                </el-button>
              </span>
            </el-dialog>

            <!--编辑基本信息模态框有的css样式复用了优惠权益-->
            <el-dialog
              title="编辑基本信息"
              :visible.sync="is_Editing_Basic_Information"
              width="40%"
            >
              <template slot="title">
                <div class="hy_model1_Editing_info">
                  <div>编辑基本信息</div>
                </div>
              </template>
              <div class="vip_info1">
                <div class="vip_info1_font1">姓名</div>
                <input
                  type="text"
                  placeholder="请输入"
                  v-model="vipDetailsObj.member_name"
                  class="vip_info1_same1 vip_info1_font2"
                />
              </div>
              <div class="vip_info1">
                <div class="vip_info1_font1">备注名</div>
                <input
                  type="text"
                  placeholder="请输入"
                  v-model="vipDetailsObj.remarks_name"
                  class="vip_info1_same1 vip_info1_font3"
                />
              </div>
              <div class="vip_info1">
                <div class="vip_info1_font1">客户编号</div>
                <input
                  type="text"
                  placeholder="请输入"
                  v-model="vipDetailsObj.member_number"
                  class="vip_info1_same1 vip_info1_font4"
                />
              </div>
              <div class="vip_info3">
                <div class="vip_info1_font5">客户来源</div>
                <div class="vip_info3_font1">
                  <el-select
                    v-model="vipDetailsObj.member_source"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item,index) in vip_source_name_model"
                      :key="item.id"
                      :label="item.source_name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div class="vip_info1">
                <div class="vip_info1_font1">性别</div>
                <div class="vip_info1_font6">
                  <span
                    class="iconfont iconnan vip_info1_font7"
                    :class="vipDetailsObj.sex == 1 ? 'model1_sex_class':''"
                    @click="model1_info_sex_chioce_nan"
                  >
                    &emsp;男
                  </span>
                  <span
                    class="iconfont iconnv vip_info1_font8"
                    :class="vipDetailsObj.sex == 2 ? 'model1_sex_class':''"
                    @click="model1_info_sex_chioce_nv"
                  >
                    &emsp;女
                  </span>
                </div>
              </div>
              <div
                style="
                  width: 100%;
                  height: 20px;
                  background: rgba(249, 249, 249, 1);
                "
              ></div>
              <div class="vip_info4">
                <div class="vip_info1_font5">会员等级</div>
                <div class="vip_info4_font1">
                  <el-select v-model="vipDetailsObj.level" placeholder="请选择">
                    <el-option
                      v-for="item in vip_grade_model_name"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div class="vip_info2">
                <div class="vip_info1_font1">
                  锁定会员等级，等级不随成长值变化
                </div>
                <div>
                  <el-switch
                    v-model="vip_details_lock"
                    active-color="#3e63dd"
                    inactive-color="#999999"
                  ></el-switch>
                </div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="hy_model1_over_info">取 消</el-button>
                <el-button type="primary" @click="hy_model1_save_info">
                  确 定
                </el-button>
              </span>
            </el-dialog>

            <!--会员绑定-->
            <el-dialog
              title="实体卡绑定"
              :visible.sync="vip_model1_bangding"
              width="500px"
            >
              <template slot="title">
                <div class="vip_model1_bangding_class">实体卡绑定</div>
              </template>
              <div class="hy_model1_shitika">
                <div class="hy_model1_shitika1">
                  <div class="hy_model1_shitika1_font1">实体卡编号:</div>
                  <div class="hy_model1_shitika1_font2">
                    <el-input
                      v-model="member_card_num"
                      placeholder="请输入实体卡编号"
                    ></el-input>
                  </div>
                </div>
                <div class="hy_model1_shitika2">
                  <div class="hy_model1_shitika2_font1">实体卡标识:</div>
                  <div class="hy_model1_shitika2_font2">
                    <el-input
                      v-model="member_card_voucher"
                      placeholder="请刷卡获取实体卡标识"
                      maxlength="10"
                    ></el-input>
                  </div>
                </div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDialog">取 消</el-button>
                <el-button type="primary" @click="bindEntityCard">
                  确 定
                </el-button>
              </span>
            </el-dialog>

            <!--会员解绑-->
            <el-dialog
              title="解除绑定"
              :visible.sync="vip_model_cancel_bangding"
              width="500px"
            >
              <template slot="title">
                <div class="vip_model1_bangding_class">解除绑定</div>
              </template>
              <div class="hy_model1_shitika">
                <div class="hy_model1_shitika2">
                  <div class="hy_model1_shitika2_font1">实体卡标识:</div>
                  <div class="hy_model1_shitika2_font2">
                    <el-input
                      v-model="member_card_voucher"
                      placeholder="请输入刷卡获取实体卡标识"
                      maxlength="10"
                    ></el-input>
                  </div>
                </div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDialog">取 消</el-button>
                <el-button type="primary" @click="cancelBindEntityCard">
                  确 定
                </el-button>
              </span>
            </el-dialog>

            <!--会员详情充值卡详情-->
            <el-dialog
              title="会员余额"
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :visible.sync="is_details_cz_open"
              append-to-body
            >
              <template slot="title">
                <div class="hy_model1_title_cz">
                  <div
                    class="iconfont iconiconfontyoujiantou"
                    @click="back_vip_details"
                  >
                    返回
                  </div>
                  <div class="hy_model1_cz_top1">会员余额</div>
                  <div style="visibility: hidden">隐藏</div>
                </div>
              </template>
              <div class="main">
                <!--借鉴收银台充值页面-->
                <ul class="main-right">
                  <!---->
                  <li style="display: flex; width: 100%">
                    <!--选择订单内容-->
                    <div class="server">
                      <div class="vip_model2_left">
                        <div class="vip_model3_left1">
                          <div class="vip_model3_left1_fon1">充值卡</div>
                          <div class="vip_model3_left1_fon2">2019-3-26开卡</div>
                        </div>
                        <div class="vip_model3_left2">基本信息</div>
                        <div class="vip_model3_left3">
                          <div class="vip_model3_left3_font1">名称</div>
                          <div class="vip_model3_left3_font2">132</div>
                        </div>
                        <div class="vip_model3_left4">
                          <div class="vip_model3_left4_font1">编号</div>
                          <div class="vip_model3_left4_font2">
                            451451451451451541
                          </div>
                        </div>
                        <div class="vip_model3_left5">
                          <div class="vip_model3_left5_font1">状态</div>
                          <div class="vip_model3_left5_font2">出售中</div>
                        </div>
                        <div class="vip_model3_left6">
                          <div class="vip_model3_left6_font1">网店展示</div>
                          <div class="vip_model3_left6_font2">展示</div>
                        </div>
                        <div class="vip_model3_left7">
                          <div class="vip_model3_left7_font1">开卡价格</div>
                          <div class="vip_model3_left7_font2">￥{{}}</div>
                        </div>
                        <div class="vip_model3_left8">
                          <div class="vip_model3_left8_font1">适用门店</div>
                          <div class="vip_model3_left8_font2">所有门店</div>
                        </div>
                        <div class="vip_model3_left9">使用须知</div>
                        <div class="vip_model3_left10">请在有效期内使用</div>
                      </div>
                    </div>
                    <!--分割线-->
                    <div class="menu_line"></div>
                    <!--开单内容-->
                    <div class="open_details">
                      <div style="padding: 20px 20px">
                        <el-table :data="vip_model3_yue" style="width: 100%">
                          <el-table-column
                            prop="name"
                            label="服务名称"
                            width="233px"
                          ></el-table-column>
                          <el-table-column
                            prop="kaika_time"
                            label="开卡次数"
                            width="233px"
                          ></el-table-column>
                          <el-table-column
                            prop="have_time"
                            label="剩余次数"
                            width="233px"
                          ></el-table-column>
                          <el-table-column
                            prop="validity_time"
                            label="有效期"
                          ></el-table-column>
                        </el-table>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </el-dialog>

            <!--余额明细-->
            <el-dialog
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :header-cell-style="vipClass"
              :visible.sync="isMemberBalance"
              append-to-body
            >
              <div class="hy_model1_title_cz">
                <div
                  class="iconfont iconiconfontyoujiantou"
                  @click="back_vip_model1_details"
                >
                  返回
                </div>
                <div class="hy_model1_cz_top1">余额明细</div>
                <div style="visibility: hidden">隐藏</div>
              </div>
              <div style="padding: 10px 40px 15px 40px">
                <template>
                  <el-table
                    :data="memberBalanceData"
                    class="memberBalanceStyle"
                    style="width: 100%"
                  >
                    <el-table-column
                      prop="addtime"
                      label="创建时间"
                      width="200"
                    ></el-table-column>
                    <el-table-column
                      prop="store_id"
                      label="归属门店"
                      width="180"
                    ></el-table-column>
                    <el-table-column
                      prop="change_type"
                      :show-overflow-tooltip="true"
                      label="类型"
                    ></el-table-column>
                    <el-table-column label="本金（元）" width="140">
                      <template slot-scope="scope">
                        <span v-if="scope.row.change_type.indexOf('减')!=-1">
                          -￥{{scope.row.capital_balance}}
                        </span>
                        <span v-if="scope.row.change_type.indexOf('加')!=-1">
                          +￥{{scope.row.capital_balance}}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="赠额（元）" width="140">
                      <template slot-scope="scope">
                        <span v-if="scope.row.change_type.indexOf('减')!=-1">
                          -￥{{scope.row.present_balance}}
                        </span>
                        <span v-if="scope.row.change_type.indexOf('加')!=-1">
                          +￥{{scope.row.present_balance}}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="变动总额（元）" width="140">
                      <template slot-scope="scope">
                        <span v-if="scope.row.change_type.indexOf('减')!=-1">
                          -￥{{scope.row.total}}
                        </span>
                        <span v-if="scope.row.change_type.indexOf('加')!=-1">
                          +￥{{scope.row.total}}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="remark"
                      label="备注"
                      :show-overflow-tooltip="true"
                    ></el-table-column>
                    <el-table-column label="操作">
                      <template slot-scope="scope">
                        <el-button
                          v-if="scope.row.order_type==2"
                          disabled
                          size="mini"
                          @click="handleDetail(scope.$index, scope.row)"
                        >
                          预约详情
                        </el-button>
                        <el-button
                          v-if="scope.row.order_type==1"
                          size="mini"
                          @click="handleDetail(scope.$index, scope.row)"
                        >
                          订单详情
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div
                    class="memberBalanceFengye"
                    v-if="memberBalanceData.length>0 || memberBalanceCount>0"
                  >
                    <el-pagination
                      background
                      @size-change="handleBalanceSizeChange"
                      @current-change="handleBalanceCurrentChange"
                      :page-size="balanceLimit"
                      :current-page="balanceCurrentPage"
                      layout="total, prev, pager, next, jumper"
                      :total="memberBalanceCount"
                    ></el-pagination>
                  </div>
                </template>
              </div>
            </el-dialog>
            <!--会员卡页面-->
            <el-dialog
              title="会员卡"
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :visible.sync="is_model1_vipka"
              append-to-body
            >
              <template slot="title">
                <div class="hy_model1_title_cz">
                  <div
                    class="iconfont iconiconfontyoujiantou"
                    @click="back_vip_model1_details"
                  >
                    返回
                  </div>
                  <div class="hy_model1_cz_top1">会员卡</div>
                  <div style="visibility: hidden">隐藏</div>
                </div>
              </template>
              <div class="main" style="height: auto">
                <div class="hy_model1_huiyuanka">
                  <div class="hy_model4_top">
                    <el-radio-group
                      v-model="changeModelLabel"
                      @change="change_model4_top_num"
                    >
                      <el-radio-button label="有效">
                        有效&emsp;（{{memberCardLength}}）
                      </el-radio-button>
                      <el-radio-button label="失效">
                        失效&emsp;（{{invalidMemberCardLength}}）
                      </el-radio-button>
                    </el-radio-group>
                  </div>
                  <div class="hy_model4_mid" v-if="model4_show_youxiao">
                    <div
                      :class="value.cardtype==2?'hy_model4_youxiao_bg1':'hy_model4_youxiao_bg2'"
                      v-for="(value,index) in memberCard"
                      @click="activeCard(index)"
                      v-if="memberCardLength"
                    >
                      <div class="hy_model4_youxiao_bg">
                        <div class="hy_youxiao1_font1">{{value.cardName}}</div>
                        <!-- 卡项类型：1，次卡，2，充值卡-->
                        <!-- 次卡类型：1，有限次卡，2，无限次卡，3，通卡-->
                        <div class="hy_youxiao1_font2" v-if="value.cardtype==2">
                          充值卡
                        </div>
                        <div
                          class="hy_youxiao1_font2"
                          v-if="value.cardtype==1 && value.once_cardtype==1"
                        >
                          有限次卡
                        </div>
                        <div
                          class="hy_youxiao1_font2"
                          v-if="value.cardtype==1 && value.once_cardtype==2"
                        >
                          无限次卡
                        </div>
                        <div
                          class="hy_youxiao1_font2"
                          v-if="value.cardtype==1 && value.once_cardtype==3"
                        >
                          通卡
                        </div>

                        <div class="hy_youxiao1_font3">{{value.storeName}}</div>
                        <div class="hy_youxiao1_font4">
                          {{value.IndateName}}
                        </div>
                      </div>
                    </div>
                    <div v-if="!memberCardLength">暂无会员卡</div>
                  </div>
                  <div class="hy_model4_mid" v-else="model4_show_youxiao">
                    <div
                      :class="value.cardtype==2?'hy_model4_youxiao_bg1':'hy_model4_youxiao_bg2'"
                      v-for="(value,index) in invalidMemberCard"
                      @click="activeCard(index)"
                      v-if="invalidMemberCardLength"
                    >
                      <div class="hy_model4_youxiao_bg">
                        <div class="hy_youxiao1_font1">{{value.cardName}}</div>
                        <!-- 卡项类型：1，次卡，2，充值卡-->
                        <!-- 次卡类型：1，有限次卡，2，无限次卡，3，通卡-->
                        <div class="hy_youxiao1_font2" v-if="value.cardtype==2">
                          充值卡
                        </div>
                        <div
                          class="hy_youxiao1_font2"
                          v-if="value.cardtype==1 && value.once_cardtype==1"
                        >
                          有限次卡
                        </div>
                        <div
                          class="hy_youxiao1_font2"
                          v-if="value.cardtype==1 && value.once_cardtype==2"
                        >
                          无限次卡
                        </div>
                        <div
                          class="hy_youxiao1_font2"
                          v-if="value.cardtype==1 && value.once_cardtype==3"
                        >
                          通卡
                        </div>

                        <div class="hy_youxiao1_font3">{{value.storeName}}</div>
                        <div class="hy_youxiao1_font4">
                          {{value.IndateName}}
                        </div>
                      </div>
                    </div>
                    <div v-if="!invalidMemberCardLength">暂无会员卡</div>
                  </div>
                </div>
              </div>
            </el-dialog>
            <el-dialog
              ref="activeRefund"
              title="查看卡项"
              :visible.sync="showOrderCardData"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              append-to-body
            >
              <template slot="title">
                <div class="hy_model1_title_cz">
                  <div
                    class="iconfont iconiconfontyoujiantou"
                    @click="showOrderCardData=false"
                  >
                    返回
                  </div>
                  <div
                    class="hy_model1_cz_top1"
                    @click="showOrderCardData=false"
                  >
                    卡项详情
                  </div>
                  <div style="visibility: hidden">隐藏</div>
                </div>
              </template>
              <el-card
                class="box-card"
                shadow="never"
                v-if="orderCardData && orderCardData.id"
              >
                <el-form
                  ref="form"
                  :model="orderCardData"
                  label-width="120px"
                  size="mini"
                >
                  <el-row>
                    <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                      <el-form-item label="卡项名称：">
                        <span v-text="orderCardData.card_name"></span>
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                      <el-form-item label="卡项类型：">
                        <span
                          v-if="orderCardData.cardtype==1 && orderCardData.cardid>0"
                        >
                          次卡
                        </span>
                        <span v-if="orderCardData.cardtype==2">充值卡</span>
                        <span
                          v-if="orderCardData.cardtype==1 && orderCardData.cardid==0"
                        >
                          充卡
                        </span>
                        <span
                          v-if="orderCardData.cardtype==1 && orderCardData.cardid==-1"
                        >
                          导入卡项
                        </span>
                        <span
                          v-if="orderCardData.cardtype==1 && orderCardData.cardid==-3"
                        >
                          获客营销
                        </span>
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                      <el-form-item label="有效期：">
                        <span v-text="orderCardData.indateName"></span>
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                      <el-form-item label="购卡时间：">
                        <span v-text="orderCardData.buyTimeName"></span>
                      </el-form-item>
                    </el-col>
                    <template v-if="orderCardData.cardtype==2">
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                        <el-form-item label="购卡价格：">
                          <span v-text="orderCardData.buyMoney"></span>
                        </el-form-item>
                      </el-col>
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                        <el-form-item label="总金额：">
                          <span
                            v-text="toMoney(orderCardData.totalbalance)"
                          ></span>
                        </el-form-item>
                      </el-col>
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                        <el-form-item label="已使用：">
                          <span
                            v-text="toMoney(orderCardData.usebalance)"
                          ></span>
                        </el-form-item>
                      </el-col>
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                        <el-form-item label="剩余总额：">
                          <span
                            v-text="toMoney(orderCardData.residuebalance)"
                          ></span>
                        </el-form-item>
                      </el-col>
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                        <el-form-item label="剩余本金：">
                          <span
                            v-text="toMoney(orderCardData.capitalbalance)"
                          ></span>
                        </el-form-item>
                      </el-col>
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :span="12">
                        <el-form-item label="剩余赠送：">
                          <span
                            v-text="toMoney(orderCardData.presentbalance)"
                          ></span>
                        </el-form-item>
                      </el-col>
                    </template>
                    <el-col
                      :xs="24"
                      :sm="24"
                      :md="24"
                      :lg="24"
                      :span="24"
                      id="cardsuccess"
                    >
                      <el-table
                        :data="orderCardData.cardDetail"
                        style="width: 100%"
                      >
                        <el-table-column
                          width="350"
                          prop="goodsInfo"
                          label="商品"
                        >
                          <template slot-scope="scope">
                            <el-row type="flex" justify="start" align="middle">
                              <div style="flex-shrink: 0">
                                <img
                                  onerror="this.src='images/default.jpg'"
                                  style="width: 60px; height: 60px"
                                  :src="scope.row.goodsInfo.img"
                                  alt=""
                                />
                              </div>
                              <div style="padding: 5px 10px">
                                <template v-if="orderCardData.cardid==-3">
                                  <el-badge
                                    value="奖"
                                    v-if="scope.row.isgive==1"
                                  >
                                    <span
                                      v-text="scope.row.goodsInfo.service_name"
                                    ></span>
                                  </el-badge>
                                  <span
                                    v-else
                                    v-text="scope.row.goodsInfo.service_name"
                                  ></span>
                                </template>
                                <template v-else>
                                  <el-badge
                                    value="赠"
                                    v-if="scope.row.isgive==1"
                                  >
                                    <span
                                      v-text="scope.row.goodsInfo.service_name"
                                    ></span>
                                  </el-badge>
                                  <span
                                    v-else
                                    v-text="scope.row.goodsInfo.service_name"
                                  ></span>
                                </template>
                                <div>
                                  <template v-if="scope.row.goods_type==2">
                                    <el-tag size="mini">产品</el-tag>
                                  </template>
                                  <template v-else>
                                    <el-tag size="mini">服务</el-tag>
                                  </template>
                                </div>
                              </div>
                            </el-row>
                            <template v-if="scope.row.indateName">
                              <p
                                style="color: #cacaca"
                                v-text="scope.row.indateName"
                              ></p>
                            </template>
                          </template>
                        </el-table-column>
                        <el-table-column prop="name" label="权益内容">
                          <template slot-scope="scope">
                            <div
                              style="
                                box-sizing: border-box;
                                padding-right: 20px;
                              "
                            >
                              <div
                                v-text="scope.row.num+' 次'"
                                v-if="scope.row.isgive==1"
                              ></div>
                              <div
                                v-text="scope.row.num+' 次'"
                                v-if="scope.row.isgive==2 && scope.row.card_type==1 && scope.row.once_cardtype==1"
                              ></div>
                              <div
                                v-text="'不限次'"
                                v-if="scope.row.isgive==2 && scope.row.card_type==1 && scope.row.once_cardtype==2"
                              ></div>
                              <div
                                v-text="'以上服务共'+scope.row.num+' 次'"
                                v-if="scope.row.isgive==2 && scope.row.card_type==1
															 && scope.row.once_cardtype==3 &&
															 (scope.$index==orderCardData.cardDetail.length-1 ||(orderCardData.cardDetail[(scope.$index+1)]['isgive']==1))"
                              ></div>
                              <div
                                v-text="scope.row.discount+' 折'"
                                v-if="scope.row.isgive==2 && scope.row.card_type==2"
                              ></div>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="权益使用">
                          <template slot-scope="scope">
                            <template
                              v-if="scope.row.card_type==2 && scope.row.isgive==2"
                            >
                              <span v-text="'--'"></span>
                            </template>
                            <template v-else>
                              <span
                                v-text="'已使用'+scope.row.usenum+'次'"
                              ></span>
                            </template>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>
                  </el-row>
                </el-form>
              </el-card>
            </el-dialog>
            <!--优惠券页面-->
            <el-dialog
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :header-cell-style="vipClass"
              :visible.sync="isMemberDiscount"
              append-to-body
            >
              <div class="hy_model1_title_cz">
                <div
                  class="iconfont iconiconfontyoujiantou"
                  @click="back_vip_model1_details"
                  style="cursor: pointer"
                >
                  返回
                </div>
                <div class="hy_model1_cz_top1">优惠券</div>
                <div style="visibility: hidden">隐藏</div>
              </div>
              <div style="padding: 10px 40px 15px 40px">
                <div class="hy_model4_top">
                  <el-radio-group
                    v-model="memberDiscountLabel"
                    @change="memberDiscountChange"
                  >
                    <el-radio-button label="未使用">未使用</el-radio-button>
                    <el-radio-button label="已使用">已使用</el-radio-button>
                    <el-radio-button label="已过期">已过期</el-radio-button>
                  </el-radio-group>
                </div>
                <template>
                  <el-table
                    :data="memberDiscountData"
                    border
                    class="memberBenfitStyle"
                    style="width: 100%"
                  >
                    <el-table-column
                      prop="coupon_type"
                      :show-overflow-tooltip="true"
                      label="类型"
                      width="100"
                    >
                      <template slot-scope="scope">
                        <span v-if="scope.row.coupon_type == 1">现金券</span>
                        <span v-else-if="scope.row.coupon_type == 2">
                          兑换券
                        </span>
                        <span v-else="scope.row.coupon_type == 3">待定</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="get_time"
                      label="领取时间"
                      width="200"
                    ></el-table-column>
                    <el-table-column
                      prop="coupon_name"
                      label="名称"
                      width="160"
                    ></el-table-column>
                    <el-table-column
                      prop="faceValue"
                      :show-overflow-tooltip="true"
                      label="价值"
                    ></el-table-column>
                    <el-table-column
                      prop="costInfo"
                      label="使用条件"
                      width="200"
                    ></el-table-column>
                    <el-table-column
                      prop="expireTime"
                      label="有效期"
                      width="200"
                    ></el-table-column>
                    <el-table-column
                      label="可用门店"
                      :show-overflow-tooltip="true"
                      width="120"
                    >
                      <template slot-scope="scope">
                        <span v-for="(item,index) in scope.row.storeInfo">
                          {{item.storetag}}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div
                    class="memberBalanceFengye"
                    v-if="memberDiscountData.length>0 || memberDiscountCount>0"
                  >
                    <el-pagination
                      background
                      @size-change="handleDiscountSizeChange"
                      @current-change="handleDiscountCurrentChange"
                      :page-size="discountLimit"
                      :current-page="discountCurrentPage"
                      layout="total, prev, pager, next, jumper"
                      :total="memberDiscountCount"
                    ></el-pagination>
                  </div>
                </template>
              </div>
            </el-dialog>

            <!--会员权益-->
            <el-dialog
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :header-cell-style="vipClass"
              :visible.sync="isMemberBenfit"
              append-to-body
            >
              <div class="hy_model1_title_cz">
                <div
                  class="iconfont iconiconfontyoujiantou"
                  @click="back_vip_model1_details"
                >
                  返回
                </div>
                <div class="hy_model1_cz_top1">会员权益</div>
                <div style="visibility: hidden">隐藏</div>
              </div>
              <div style="padding: 10px 40px 15px 40px">
                <div class="hy_model4_top">
                  <el-radio-group
                    v-model="memberBenfitLabel"
                    @change="memberBenfitChange"
                  >
                    <el-radio-button label="可使用">
                      可使用&emsp;（{{benfitLength}}）
                    </el-radio-button>
                    <el-radio-button label="已过期">
                      已过期&emsp;（{{invalidBenfitLength}}）
                    </el-radio-button>
                  </el-radio-group>
                </div>
                <template>
                  <el-table
                    :data="memberBenfitData"
                    border
                    class="memberBenfitStyle"
                    style="width: 100%"
                  >
                    <el-table-column
                      prop="goodsName"
                      :show-overflow-tooltip="true"
                      label="权益"
                      width="200"
                    ></el-table-column>
                    <el-table-column
                      prop="usenum"
                      label="使用情况"
                      width="120"
                    ></el-table-column>
                    <el-table-column
                      prop="maxnum"
                      label="剩余次数"
                      width="120"
                    ></el-table-column>
                    <el-table-column :show-overflow-tooltip="true" label="来源">
                      <template slot-scope="scope">
                        <p v-if="scope.row.source">{{scope.row.source}}</p>
                        <p v-if="scope.row.card_name">
                          {{scope.row.card_name}}
                        </p>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="IndateName"
                      label="有效期"
                      width="200"
                    ></el-table-column>
                    <el-table-column
                      prop="addtime"
                      label="获取日期"
                      width="200"
                    ></el-table-column>
                    <el-table-column
                      prop="storeName"
                      label="适用门店"
                      :show-overflow-tooltip="true"
                    ></el-table-column>
                  </el-table>
                  <div
                    class="memberBalanceFengye"
                    v-if="memberBenfitData.length>0 || memberBenfitCount>0"
                  >
                    <el-pagination
                      background
                      @size-change="handleBenfitSizeChange"
                      @current-change="handleBenfitCurrentChange"
                      :page-size="balanceLimit"
                      :current-page="balanceCurrentPage"
                      layout="total, prev, pager, next, jumper"
                      :total="memberBenfitCount"
                    ></el-pagination>
                  </div>
                </template>
              </div>
            </el-dialog>

            <!--欠账-->
            <el-dialog
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :header-cell-style="vipClass"
              :visible.sync="isDebt"
              append-to-body
            >
              <div class="hy_model1_title_cz">
                <div
                  style="width: 30%"
                  class="iconfont iconiconfontyoujiantou"
                  @click="back_vip_model1_details"
                >
                  返回
                </div>
                <div class="hy_model1_cz_top1">欠账记录</div>
                <div style="width: 30%; text-align: right">
                  <el-button
                    @click="doToRePayArr"
                    type="text"
                    v-if="rePayArrMoney>0"
                  >
                    批量还款（{{rePayArrMoney}}元 | {{rePayArr.length}}单）
                  </el-button>
                </div>
              </div>
              <div style="padding: 10px 40px 15px 40px">
                <div class="hy_model4_top">
                  <el-radio-group
                    v-model="memberDebtLabel"
                    @change="memberDebtChange"
                  >
                    <el-radio-button label="待还款">待还款</el-radio-button>
                    <el-radio-button label="已还款">已还款</el-radio-button>
                  </el-radio-group>
                </div>
                <template>
                  <el-table
                    :data="memberDebtData"
                    class="memberBenfitStyle"
                    style="width: 100%"
                  >
                    <el-table-column label="客户">
                      <template slot-scope="scope">
                        <p v-if="scope.row.vip">
                          {{scope.row.vip.member_name}}
                        </p>
                        <p v-if="scope.row.vip">{{scope.row.vip.phone}}</p>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="storeTag"
                      label="下单门店"
                    ></el-table-column>
                    <el-table-column
                      prop="collection_time"
                      label="付款时间"
                    ></el-table-column>
                    <el-table-column
                      prop="cashier"
                      label="收银员"
                    ></el-table-column>
                    <el-table-column label="订单金额">
                      <template slot-scope="scope">
                        <span>￥{{scope.row.receivable}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="memberDebtLabel=='待还款'"
                      label="欠款金额"
                    >
                      <template slot-scope="scope">
                        <span>￥{{scope.row.debt_value}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="memberDebtLabel=='已还款'"
                      label="还款金额"
                    >
                      <template slot-scope="scope">
                        <span>￥{{scope.row.debt_value}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                      <template slot-scope="scope">
                        <div>
                          <el-button
                            @click="seeDebtDetail(scope.row)"
                            type="text"
                            size="small"
                          >
                            查看
                          </el-button>
                          <el-button
                            type="text"
                            size="small"
                            v-if="memberDebtLabel=='待还款'"
                            @click="rePayDebt(scope.row)"
                          >
                            还款
                          </el-button>
                        </div>
                        <div v-if="memberDebtLabel=='待还款'">
                          <el-button
                            type="text"
                            v-if="!inRePayArr(scope.row)"
                            size="small"
                            @click="addToRePayArr(scope.row)"
                          >
                            加入还款列表
                          </el-button>
                          <el-button
                            type="text"
                            v-else
                            size="small"
                            @click="removeFromRePayArr(scope.row)"
                          >
                            移出还款列表
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="memberBalanceFengye" v-if="memberDebtCount>0">
                    <el-pagination
                      background
                      @size-change="handleDebtSizeChange"
                      @current-change="handleDebtCurrentChange"
                      :page-size="debtLimit"
                      :current-page="debtCurrentPage"
                      layout="total, prev, pager, next, jumper"
                      :total="memberDebtCount"
                    ></el-pagination>
                  </div>
                </template>
              </div>
            </el-dialog>

            <!--会员成长值-->
            <el-dialog
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :header-cell-style="vipClass"
              :visible.sync="isGroupValue"
              append-to-body
            >
              <div class="hy_model1_title_cz">
                <div
                  class="iconfont iconiconfontyoujiantou"
                  @click="back_vip_model1_details"
                >
                  返回
                </div>
                <div class="hy_model1_cz_top1">成长值</div>
                <div style="visibility: hidden">隐藏</div>
              </div>
              <div style="padding: 10px 40px 15px 40px">
                <template>
                  <el-table
                    :data="memberGroupValueData"
                    border
                    class="memberBalanceStyle"
                    style="width: 100%"
                  >
                    <el-table-column
                      :show-overflow-tooltip="true"
                      prop="reason"
                      label="变动原因"
                    ></el-table-column>
                    <el-table-column label="变动成长值">
                      <template slot-scope="scope">
                        <span v-if="scope.row.growth_value>0">
                          +{{scope.row.growth_value}} 点
                        </span>
                        <span v-if="scope.row.growth_value<0">
                          {{scope.row.growth_value}} 点
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="storeName"
                      :show-overflow-tooltip="true"
                      label="获取门店"
                    ></el-table-column>
                    <el-table-column
                      prop="addtime"
                      label="变动时间"
                    ></el-table-column>
                  </el-table>
                  <div
                    class="memberBalanceFengye"
                    v-if="memberGroupValueData.length>0 || memberGroupValueCount>0"
                  >
                    <el-pagination
                      background
                      @size-change="handleGroupSizeChange"
                      @current-change="handleGroupCurrentChange"
                      :page-size="groupValueLimit"
                      :current-page="groupValuePage"
                      layout="total, prev, pager, next, jumper"
                      :total="memberGroupValueCount"
                    ></el-pagination>
                  </div>
                </template>
              </div>
            </el-dialog>

            <!--会员积分-->
            <el-dialog
              top="0px"
              :show-close="is_chacha1"
              :fullscreen="isfullscreen2"
              :header-cell-style="vipClass"
              :visible.sync="isGroupScore"
              append-to-body
            >
              <div class="hy_model1_title_cz">
                <div
                  class="iconfont iconiconfontyoujiantou"
                  @click="back_vip_model1_details"
                >
                  返回
                </div>
                <div class="hy_model1_cz_top1">积分记录</div>
                <div style="visibility: hidden">隐藏</div>
              </div>
              <div style="padding: 10px 40px 15px 40px">
                <template>
                  <el-table
                    :data="memberGroupScoreData"
                    border
                    class="memberBalanceStyle"
                    style="width: 100%"
                  >
                    <el-table-column label="积分变动类型">
                      <template slot-scope="scope">
                        <span v-if="scope.row.type==1">购买充值</span>
                        <span v-if="scope.row.type==2">购买卡项</span>
                        <span v-if="scope.row.type==3">付费会员</span>
                        <span v-if="scope.row.type==4">购买产品</span>
                        <span v-if="scope.row.type==5">购买服务</span>
                        <span v-if="scope.row.type==6">每日签到</span>
                        <span v-if="scope.row.type==10">退款</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="变动积分">
                      <template slot-scope="scope">
                        <span v-if="scope.row.score_value>0">
                          +{{scope.row.score_value}}点
                        </span>
                        <span v-if="scope.row.score_value<0">
                          -{{scope.row.score_value}}点
                        </span>
                      </template>
                    </el-table-column>

                    <el-table-column
                      prop="storeName"
                      :show-overflow-tooltip="true"
                      label="获取门店"
                    ></el-table-column>
                    <el-table-column
                      :show-overflow-tooltip="true"
                      prop="reason"
                      label="变动原因"
                    ></el-table-column>
                    <el-table-column
                      prop="addtime"
                      label="变动时间"
                    ></el-table-column>
                  </el-table>
                  <div
                    class="memberBalanceFengye"
                    v-if="memberGroupScoreData.length>0 || memberGroupScoreCount>0"
                  >
                    <el-pagination
                      background
                      @size-change="handleGroupScoreSizeChange"
                      @current-change="handleGroupScoreCurrentChange"
                      :page-size="groupScoreLimit"
                      :current-page="groupScorePage"
                      layout="total, prev, pager, next, jumper"
                      :total="memberGroupScoreCount"
                    ></el-pagination>
                  </div>
                </template>
              </div>
            </el-dialog>

            <!-- 点击收银台弹框 -->
            <template v-if="buy_receipt">
              <app-pay
                :buy-receipt="buy_receipt"
                :login-info="loginInfo"
                :use-card="isRechargeCard"
                :order-no="orderNo"
                :bill-to-pay="billToPay"
                :is-debt-flag="isPayDebt"
                @close-pay="bindClosePay"
              ></app-pay>
            </template>
          </div>
          <!--is_Editing_Basic_Information-->
        </div>
      </div>
    </div>
    <script src="./vue/vue2.5.16.js"></script>
    <script src="./vue/element/<EMAIL>"></script>
    <script src="./js/plugin/jquery-3.2.1.min.js"></script>
    <script src="js/unocss.theme.js"></script>
    <script src="js/plugin/<EMAIL>"></script>
    <script src="js/ff_util.js"></script>
    <script src="component/components.js"></script>
    <script src="print/print.js"></script>
    <script src="js/vip.js"></script>
    <script type="text/javascript" src="js/plugin/LodopFuncs.js"></script>
    <script src="js/plugin/Qrcode.js"></script>
    <script src="js/plugin/JsBarcode.js"></script>
  </body>
</html>
