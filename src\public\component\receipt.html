<div class="receipt-wrap" v-cloak>
  <div class="zj_font1">
    <div class="zj_font4">收款</div>
    <!-- <div class="zj_xuanze_vip" @click="selectMember">选择会员</div> -->
    <el-button
      class="absolute right-6 top-3"
      type="primary"
      @click="isShowMemberSearch = true"
    >
      客户列表
    </el-button>
  </div>
  <div class="key-wrap">
    <div class="zj_font2">先填写应收金额,然后下一步收款</div>
    <el-row type="flex" justify="center">
      <el-col :sm="20" :md="18" :lg="14">
        <div class="zj_show_price">
          <span class="zj_show_price_font1">实收</span>
          <div class="zj_show_price_font2">
            <span>￥</span>
            <input
              type="text"
              ref="moneyInner"
              v-model.trim="zj_shou_qian"
              @keyup.enter="directCollection"
              @input="manualPrice"
            />
            <div
              class="member_info"
              v-show="receiptMemberInfo && receiptMemberInfo.id"
            >
              <i class="el-icon-remove" @click="del_receiptMember"></i>
              <div>
                <p style="margin-bottom: 8px">
                  {{receiptMemberInfo.member_name}}
                </p>
                <p>{{receiptMemberInfo.phone}}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="zj_price_menu">
          <div class="zj_menu_num">
            <ul>
              <li v-for="(value,index) in zj_price_menu" style="display: flex">
                <p
                  :class="value.key1 == '00' ? 'zj_two_zero' : 'zj_num_1'"
                  @click="zj_input_num(index,value1)"
                  :id="value1"
                >
                  {{value.key1}}
                </p>
                <p
                  :class="value.key2 == '0' ? 'zj_one_zero' : 'zj_num_2'"
                  @click="zj_input_num(index,value2)"
                  :id="value2"
                >
                  {{value.key2}}
                </p>
                <p
                  :class="value.key3 == '.' ? 'zj_xiao_dian' : 'zj_num_3'"
                  @click="zj_input_num(index,value3)"
                  :id="value3"
                >
                  {{value.key3}}
                </p>
              </li>
            </ul>
          </div>
          <div class="zj_menu_take">
            <ul>
              <li class="zj_menu_take_font1" @click="zj_del_all">C</li>
              <li
                class="iconfont iconshanchuyigeshuzi"
                @click="zj_del_one"
              ></li>
              <li class="zj_menu_take_font2" @click="directCollection">收款</li>
            </ul>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
  <!-- 客户列表框 -->
  <member-search-dialog
    :value="isShowMemberSearch"
    :login-info="loginInfo"
    @sync-is-show-memeber-search="isShowMemberSearch = $event"
    @handle-select-member="handleSelect"
  ></member-search-dialog>
  <!--最大的付款金额-->
  <!--<div class="zj_max_pay" v-if="zj_max_price">最大付款金额为十万</div>-->

  <!-- <el-dialog
    title="会员查询"
    :visible.sync="is_searchMember"
    width="500px"
    :show-close="false"
  >
    <div>
      <el-input
        autofocus
        placeholder="输入搜索的会员手机号或刷实体卡"
        ref="refSearchMember"
        v-model.trim="receipt_searchMember"
        @input="receipt_search(receipt_searchMember)"
        @keyup.enter.native="receipt_enter(receipt_searchMember)"
      ></el-input>
      <ul
        class="receiptMemberInfo"
        v-show="receiptMemberInfo && receiptMemberInfo.id"
      >
        <li>会员名称：{{receiptMemberInfo.member_name}}</li>
        <li>会员手机：{{receiptMemberInfo.phone}}</li>
        <li>会员等级：{{receiptMemberInfo.memberLevelName}}</li>
        <li>消费总额：{{receiptMemberInfo.total | filterMoney}}</li>
      </ul>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="cancel-btn" @click="cancelSearchMember">
        取 消
      </el-button>
      <el-button type="primary" @click="is_searchMember = false">
        确 定
      </el-button>
    </span>
  </el-dialog> -->
</div>
