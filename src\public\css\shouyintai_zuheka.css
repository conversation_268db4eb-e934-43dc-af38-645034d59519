.menu {
  height: 90px;
  background-color: #2b282c;
  display: flex;
  overflow: hidden;
}

.menu_font {
  margin-left: 161px;
  display: flex;
  overflow: hidden;
}

.menu_font li {
  width: calc(100vh / 5.5);
  height: 90px;
  color: #ffffff;
  text-align: center;
  line-height: 90px;
  font-size: 24px;
}

.menu_font li:hover {
  cursor: pointer;
}

.addclass {
  background-color: #3e63dd;
}

.cashier {
  background: #2b282c;
  opacity: 0.95;
  height: 90px;
}

.cashier ul {
  display: flex;
  overflow: hidden;
  margin-left: 16px;
}

.cashier ul .li1 {
  color: white;
  width: 66px;
  font-size: 18px;
  text-align: center;
  line-height: 90px;
  margin-left: 23px;
}

.cashier ul .li2 {
  color: white;
  width: 142px;
  font-size: 18px;
  text-align: center;
  line-height: 90px;
  margin-left: 23px;
}

.main {
  display: flex;
  overflow: hidden;
}

.left {
  width: 162px;
  height: calc(100vh - 90px);
  /*overflow: hidden;*/
}

.left_menu {
  width: 162px;
  height: calc(100vh - 174px);
  overflow: hidden;
  margin-top: 8px;
  background: rgba(43, 40, 44, 1);
  border-radius: 0px 20px 20px 0px;
  padding-top: 76px;
}

.left_menu ul li {
  color: white;
  font-size: 18px;
  height: 80px;
  text-align: center;
  line-height: 80px;
}

.left_menu ul li:hover {
  cursor: pointer;
}

/* 主体--右侧 */
.main-right {
  flex: 1;
}

.server {
  width: 580px;
  height: calc(100vh - 90px);
}

.server_chioce {
  width: 580px;
  height: 90px;
  border: 1px solid rgba(255, 255, 255, 1);
  line-height: 90px;
  text-align: center;
  font-size: 26px;
  margin: auto;
  font-weight: 400;
  color: rgba(0, 0, 0, 1);
}

.b_left {
  width: 120px;
  height: 50px;
  border: 1px solid #3e63dd;
  border-radius: 8px 0px 0px 8px;
  text-align: center;
  line-height: 50px;
  color: #3e63dd;
}

.b_left:hover {
  cursor: pointer;
}

.b_right {
  width: 120px;
  height: 50px;
  /*border: 1px solid green;*/
  border: 1px solid #3e63dd;
  border-radius: 0px 8px 8px 0px;
  text-align: center;
  line-height: 50px;
  color: #3e63dd;
}

.b_right {
  cursor: pointer;
}

.server_bg {
  background-color: #3e63dd;
  color: #ffffff;
}

.server_line1 {
  width: 580px;
  height: 2px;
  background: rgba(238, 238, 238, 1);
}

.search_menu {
  width: 540px;
  height: calc(100vh - 202px);
  overflow: hidden;
  /*border: 1px solid red;*/
  margin-top: 16px;
  margin-left: 20px;
}

.search_bor {
  width: 538px;
  height: 50px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 5px;
}

.search_input {
  width: 400px;
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  margin-top: 15px;
  margin-left: 25px;
}

.search_bor img {
  width: 24px;
  height: 26px;
  margin-top: 11px;
  margin-left: 30px;
}

.search_bor img:hover {
  cursor: pointer;
}

.search_label {
  width: 540px;
  /*border: 1px red solid;*/
  float: left;
  overflow: hidden;
}

.search_label ul li {
  float: left;
  overflow: hidden;
  border: 1px solid #999999;
  border-radius: 22px;
  margin-left: 10px;
  margin-top: 12px;
}

.search_label ul li {
  cursor: pointer;
}

.search_label ul li p {
  font-size: 17px;

  font-weight: 400;
  color: #666666;
  display: inline-block;
  padding: 11px 32px 11px 32px;
}

.bg_label {
  background: #3e63dd;
}

/*swiper测试*/
/*.swiper-container {*/
/*width: 600px;*/
/*height: 300px;*/
/*}*/
.search_detail {
  width: 540px;
  margin-top: 16px;

  /*border: 1px solid red;*/
}
.search_detail .search_detail1 {
  cursor: pointer;
}
.search_detail .search_detail1 .serach_detail_info {
  display: flex;
  overflow: hidden;
}

.search_detail .search_detail1 .serach_detail_info img {
  display: inline-block;
  width: 110px;
  height: 100px;
  /*border: 1px solid green;*/
  margin-left: 10px;
  margin-top: 12px;
}

.search_detail .search_detail1 .serach_detail_info .serach_detail_info_font {
  margin-top: 12px;
  margin-left: 14px;
  height: 100px;
  /*border: 1px solid gold;*/
}

.search_detail
  .search_detail1
  .serach_detail_info
  .serach_detail_info_font
  .serach_detail_info_font1 {
  margin-top: 12px;
  font-size: 20px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.search_detail
  .search_detail1
  .serach_detail_info
  .serach_detail_info_font
  .serach_detail_info_font2 {
  margin-top: 32px;
  width: 91px;
  height: 18px;
  font-size: 18px;

  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  /*border: 1px solid red;*/
  display: flex;
  /*overflow: hidden;*/
}

.search_detail .search_detail1 .search_detail_line {
  width: 540px;
  border: 0.5px solid rgba(229, 229, 229, 1);
  margin-top: 12px;
}

.menu_line {
  width: 4px;
  /*height: calc(100vh - 90px);*/
  border: 1px solid rgba(238, 238, 238, 1);
  background: rgba(238, 238, 238, 1);
}

.open_details {
  flex: 1;
  height: calc(100vh - 90px);
  overflow: auto;
}

.open_details_title_font1 {
  width: 120px;
  height: 32px;
  font-size: 22px;
  font-weight: 400;
  color: rgba(0, 0, 0, 1);
  display: inline-block;
  text-align: center;
  /*border: 1px solid red;*/
}

.open_details_border {
  position: relative;
  height: 94px;
  line-height: 94px;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
}
.open_details_title {
  text-align: center;
  font-size: 30px;
  font-weight: 400;
}

.open_details_title_font2 {
  position: absolute;
  top: 11px;
  right: 18px;
  width: 96px;
  height: 24px;
  font-size: 17px;
  font-weight: 400;
  color: #3e63dd;
  line-height: 24px;
  padding: 13px 5px 13px 28px;
  border: 1px solid #3e63dd;
  border-radius: 8px;
  cursor: pointer;
}

.open_details_info {
  /*height: calc(100vh - 50px);*/
  overflow: hidden;
  box-sizing: border-box;
  padding: 18px 20px;
}
.presonal_info {
  display: flex;
  border-bottom: 1px solid rgba(187, 187, 187, 1);
}
.presonal_touxiang {
  width: 90px;
  height: 90px;
  padding: 20px 50px 20px 40px;
}
.presonal_data {
  width: 100%;
  height: 130px;
}
.presonal_tel {
  width: 100%;
  height: 64px;
  line-height: 64px;
  border-bottom: 2px solid rgba(229, 229, 229, 1);
}
.tel_input {
  border: 0;
  background: 0;
  outline: none;
  font-size: 20px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}

.presonal_name {
  width: 100%;
  height: 64px;
  line-height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search_opne_input {
  height: 48px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}
.name_input {
  border: 0;
  background: 0;
  outline: none;
  font-size: 20px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}
.xingbeinv {
  color: white;
  height: 25px;
  line-height: 25px;
  padding: 2px 2px 2px 2px;
  border: 1px #3e63dd solid;
  background: #3e63dd;
}
.xingbeinan {
  height: 25px;
  line-height: 25px;
  padding: 2px 2px 2px 2px;
  border: 1px #3e63dd solid;
  background: #3e63dd;
  color: white;
}

.open_server_name {
}

.open_details_price {
  display: flex;
  align-items: center;
  padding-right: 15px;
}

.open_shop {
  flex: 1;
  box-sizing: border-box;
  box-sizing: border-box;
  padding: 40px 15px;
  border-left: 6px solid #3e63dd;
}

.open_server_name .open_details_price {
  background: #f5f5f5;
  overflow: hidden;
  /*margin-top: 18px*/
}

.open_details_price_bg {
  background: #edf2fe;
}

.open_details_price_line {
  background: #f5f5f5;
  border: 1px solid #f5f5f5;
  width: 4px;
}

.open_details_price_line_bg {
  background: #3e63dd;
  border: 1px solid #3e63dd;
}

.font-weight {
  font-weight: 400;
}

.open_details_price_name,
.open_details_price_num,
.open_details_price_all {
  flex: 1;
}

.open_details_price_name {
  font-size: 20px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}
.open_price_name_font1 {
  padding-bottom: 10px;
}

.open_details_num {
  flex: 1;
  height: 50px;
  line-height: 50px;

  font-size: 18px;
  font-weight: 400;
  color: black;
}
.span1 {
  padding: 4px 10px 4px 10px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}
.span2 {
  padding: 4px 10px 4px 10px;
  border: 1px solid rgba(229, 229, 229, 1);
  border-left: none;
  border-right: none;
}
.span3 {
  cursor: pointer;
  padding: 4px 8px 4px 8px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(229, 229, 229, 1);
}
.span4 {
}
.open_details_prices {
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  font-weight: 400;
  color: black;
}
.open_details_price_num img {
  width: 18px;
  height: 18px;
  margin-top: 4px;
}

.open_details_price_all {
  /*width:182px;*/
  height: 25px;
  font-size: 18px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.open_details_price_all img {
  width: 18px;
  height: 18px;
  margin-top: 4px;
}

.open_details_price_del {
}

.open_details_price_del > img {
  display: inline-block;
}

.open_details_price_del img:hover {
  cursor: pointer;
}

.change_all_price {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
  padding: 0 15px;
  border-left: 6px solid #3e63dd;
}

.change_all_price_line {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  width: 4px;
}

.change_all_price_font1 {
  color: #999999;
}

.change_all_price_font2 {
  font-size: 14px;
}

.change_all_price_font2 img {
  width: 15px;
  height: 15px;
  color: #333333 !important;
  margin-top: 1px;
}

.change_all_price_font2 span {
}

.change_all_price_font3 {
  color: #999999;
  font-size: 14px;
}

.change_all_price_font3:hover {
  cursor: pointer;
}

.chioce_technician {
  display: flex;
  overflow: hidden;
  height: 60px;
  border-left: 6px solid #3e63dd;
}

.chioce_technician_line {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  width: 5px;
}

.chioce_technician_name,
.selective_sales_volume,
.batch {
  flex: 1;
}

.chioce_technician_name {
  height: 58px;
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  line-height: 58px;
}

.selective_sales_volume {
  height: 58px;
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  line-height: 58px;
}

.batch {
  height: 58px;
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  line-height: 58px;
}

.chioce_technician_name_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
}

.chioce_technician_name_font1:hover {
  cursor: pointer;
}

.chioce_technician_name_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.selective_sales_volume_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
}

.selective_sales_volume_font1:hover {
  cursor: pointer;
}

.selective_sales_volume_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.batch_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.batch_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.order_remark {
  font-size: 18px;

  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  box-sizing: border-box;
  padding: 0 18px;
}

.remark_input {
  font-size: 18px;

  font-weight: 400;
  color: rgba(187, 187, 187, 1);
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
}

.order_remark_font {
  height: 50px;
  line-height: 50px;
  font-size: 18px;
}

.order_remark_input {
  height: 59px;
  box-sizing: border-box;
  padding: 0 15px;
  border-top: 1px solid #e5e5e5;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
}

.remark_input {
  width: 100%;
  height: 100%;
  font-size: 18px;
}

.open_details_pay {
}

.chioce_Discount2 {
  height: 58px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding-left: 15px;
  border: 1px solid #e5e5e5;
}
.chioce_Discount {
  height: 58px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-left: 15px;
  border: 1px solid #e5e5e5;
}
.chioce_Discount1 {
  height: 58px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-left: 15px;
  border: 1px solid #e5e5e5;
}
.chioce_Discount_font1 {
  height: 23px;
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chioce_Discount_font0 {
  font-size: 14px;
}

.chioce_Discount_font2 {
  width: 60px;
  height: 19px;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
}

.chioce_Discount_font3 {
  width: 20px;
  height: 20px;
}
.Discount_font1_input1 {
  /*border-radius: 5px;*/
  width: 100px;
  color: rgba(51, 51, 51, 1);
}
.Discount_font1_input2 {
  /*border-radius: 5px;*/
  width: 100px;
  color: rgba(51, 51, 51, 1);
}
.chioce_Discount_font1_check {
}
.open_details_pay_choice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  padding-left: 15px;
}

.open_details_pay_choice_font4,
.open_details_pay_choice_font2,
.open_details_pay_choice_font3 {
  box-sizing: border-box;
  padding: 20px 50px;
  font-size: 20px;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
}
.open_details_pay_choice_font1 {
  color: rgba(51, 51, 51, 1);
  font-size: 20px;
}

.open_details_pay_choice_font2 {
  background: rgba(122, 122, 122, 1);
}

.open_details_pay_choice_font3 {
  background: rgba(43, 40, 44, 1);
}

.open_details_pay_choice_font4 {
  background: #3e63dd;
}

.order_three {
  display: flex;
}

/*模态框 保存*/

.save_ok {
  width: 105px;
  font-size: 25px;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  padding: 33px 100px;
  background: rgba(0, 0, 0, 1);
  opacity: 0.6;
  border-radius: 50px;
  position: absolute;
  top: 70%;
  left: 40%;
}

/*模态框 选择技师*/
.xuanze_jishi {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 24px;
  margin-bottom: 24px;
}
.xuanze_jishi_search {
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.xuanze_jishi_search input {
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  padding: 11px 0px 11px 18px;
}
.xuanze_jishi_search i {
  margin-top: 10px;
  margin-right: 17px;
  font-size: 24px;
}
.xuanze_jishi_name {
  display: flex;
  padding: 10px 0px 10px 0px;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_jishi_name_check {
}
.xuanze_jishi_name_font {
  font-size: 17px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 18px;
  cursor: pointer;
}
.xuanze_jishi_server_font {
  font-size: 17px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  cursor: pointer;
}

.xuanze_jishi_server_switch {
  margin-left: 16px;
}
.over_save {
  display: flex;
  cursor: pointer;
}
.over_save_over {
  flex: 1;
  font-size: 21px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 0px 0px 0px 7px;
  text-align: center;
  padding: 18px 0px 18px 0px;
}
.over_save_save {
  flex: 1;
  font-size: 21px;
  font-weight: 400;
  background: rgba(179, 93, 204, 1);
  border-radius: 0px 0px 7px 0px;
  text-align: center;
  padding: 18px 0px 18px 0px;
  color: rgba(255, 255, 255, 1);
}
.xuazne_xiaoshou {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 15px;
}
.xuanze_piliang {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 48px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}
.xuanze_piliang_font {
  height: calc(100vh - 500px);
}
.piliang_jishi {
  font-size: 17px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 23px 0px 23px 36px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}
.piliang_xiaoshou {
  font-size: 17px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 23px 0px 23px 36px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}
.piliang_both {
  font-size: 17px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 23px 0px 23px 36px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}
.qudan_font {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}
.detail_say {
  background: rgba(246, 246, 246, 1);
  border-radius: 4px;
  padding: 10px 30px 10px 30px;
}
.detail_ul {
  display: flex;
  justify-content: space-between;
}
.detail_ul li {
  font-size: 17px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}
.qu_dan {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 48px;
}
.shops {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
}
.shops_num {
  /*flex:1;*/
  padding: 12px 0px 12px 30px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}
.server_name {
  /*flex:1;*/
  padding: 21px 0px 21px 50px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  line-height: 38px;
}
.shops_name {
  /*flex:1;*/
  padding: 21px 0px 21px 20px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}
.shops_name_font {
  padding: 5px 0px 0px 0px;
}
.shops_servername {
  /*flex:1;*/
  padding: 21px 30px 21px 0px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}
.shops_servername_font {
  padding: 5px 0px 0px 0px;
}
.shops_price {
  /*flex:1;*/
  padding: 21px 0px 21px 0px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  line-height: 38px;
}
.shops_get_money {
  /*flex: 1;*/
  margin: 15px 5px 15px 0px;
  padding: 10px 17px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  background: #3e63dd;
  border-radius: 4px;
  text-align: center;
  line-height: 38px;
  cursor: pointer;
}
