<div>
  <el-dialog
    title="选择赠送的商品"
    :visible.sync="isChooseGift"
    custom-class="custom_dialog_give"
    top="7vh"
    :before-close="closeChooseGift"
  >
    <div style="width: 44%">
      <div class="server_chioce_give">
        <div class="server_center">
          <el-radio-group
            v-model="billingType"
            size="medium"
            @change="billingChangeType"
          >
            <el-radio-button label="1">产品</el-radio-button>
            <el-radio-button label="0">服务</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="search_menu_give" v-show="billingType==0">
        <div class="search_bor">
          <div class="el-input el-input--suffix">
            <!---->
            <input
              type="text"
              autocomplete="off"
              placeholder="请输入服务名称"
              class="el-input__inner"
              v-model.trim="search_keyword"
              ref="search_keyword"
              @keyup.enter.exact.stop="billingInquiryEnter"
            />
            <span class="el-input__suffix" @click="billingInquiryEnter">
              <span class="el-input__suffix-inner">
                <i class="el-input__icon el-icon-search"></i>
              </span>
            </span>
          </div>
        </div>
        <!--可选服务列表 uuai-->
        <div class="search_detail serverWrap_give" ref="serverWrap">
          <div
            v-infinite-scroll="loadProductMore"
            infinite-scroll-disabled="isServerGiftScroll"
            infinite-scroll-distance="10"
            infinite-scroll-immediate-check="isServerGiftScroll"
          >
            <div
              class="search_detail1"
              v-for="(value,index) in cashier_open_order_service_name"
              @click="addGiftServer(value,index)"
            >
              <div class="serach_detail_info">
                <img
                  class="serach_detail_img"
                  :src="value.imgurl"
                  onerror="this.src='images/default.jpg'"
                />
              </div>
              <div class="service_name-price">
                <p class="serach_detail_info_font1 service_name_give">
                  {{value.service_name}}
                </p>
                <p class="serach_detail_info_font2" style="color: #999999">
                  ￥{{value.price}}
                </p>
              </div>
            </div>

            <!-- 加载效果 -->
            <div v-if="busy" class="loadingtip">{{loadingtip}}</div>
          </div>
        </div>
      </div>
      <!--产品和搜索-->
      <div class="search_menu_give" v-show="billingType==1">
        <div class="search_bor">
          <div class="el-input el-input--suffix">
            <input
              type="text"
              autocomplete="off"
              placeholder="请输入产品名称"
              class="el-input__inner"
              v-model="search_product"
              ref="search_product"
              @keyup.enter.exact.stop="billingInquiryProductEnter"
            />
            <span class="el-input__suffix" @click="billingInquiryProductEnter">
              <span class="el-input__suffix-inner">
                <i class="el-input__icon el-icon-search"></i>
              </span>
            </span>
          </div>
        </div>
        <!--可选服务列表-->
        <div class="search_detail serverWrap_give" ref="productWrap">
          <div
            v-infinite-scroll="loadMoreCom"
            infinite-scroll-disabled="isProductScroll"
            infinite-scroll-distance="10"
            infinite-scroll-immediate-check="isProductScroll"
          >
            <div
              class="search_detail1 bbb"
              v-for="(value,index) in cashier_open_order_product_name"
              @click="addGiftProduct(value,index)"
            >
              <div class="serach_detail_info">
                <img
                  class="serach_detail_img"
                  :src="value.imgarr[0]"
                  onerror="this.src='images/default.jpg'"
                />
              </div>
              <div class="service_name-price">
                <p class="serach_detail_info_font1 service_name_give">
                  {{value.product_name}}
                </p>
                <p class="serach_detail_info_font2" style="color: #999999">
                  {{value.s_price}}
                </p>
              </div>
            </div>

            <!-- 加载效果 -->
            <div v-if="busyProduct" class="loadingtip">{{loadingtip}}</div>
          </div>
        </div>
      </div>
    </div>
    <!--开单内容-->
    <div class="flex-1">
      <!--开单详情标题-->
      <div class="open_details_border">
        <div class="open_details_title">赠送详情</div>
        <div class="open_details_title_font2" @click="clearPage">清空页面</div>
      </div>
      <div class="open_details_info detailsHeight_give">
        <!--内容-->
        <div class="">
          <div
            style="border-bottom: 1px solid #ccc"
            v-for="(value,index) in giftData"
          >
            <div class="open_details_price" style="background: #edf2fe">
              <div class="open_shop" style="display: flex; line-height: 34px">
                <div class="open_details_price_name_give">
                  <div
                    class="open_details_price_name_font1"
                    style="
                      width: 110px;
                      overflow-x: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    v-if="value.itemName"
                    :title="value.itemName"
                  >
                    {{value.itemName}}
                  </div>
                  <div
                    class="open_details_price_name_font2"
                    v-if="value.skuName"
                  >
                    {{value.skuName}}
                  </div>
                </div>
                <div class="open_details_price_num">
                  <el-input-number
                    style="margin-left: 40px"
                    size="mini"
                    v-model="value.num"
                    @change="giftNumChange(value.num,index)"
                    :min="1"
                    :max="20"
                    label="描述文字"
                  ></el-input-number>
                  <!-- <span v-if="value.zhonglei==1 || value.zhonglei==2">￥{{value.originPrice}}*{{value.num}}</span> -->
                </div>

                <div class="open_details_price_all_give">
                  <p style="margin-bottom: 8px">
                    小计:&nbsp;&nbsp;{{value.originPrice | filterMoney}}
                  </p>
                </div>
              </div>
              <div
                class="open_details_price_del"
                @click="open_details_price_del(index)"
              >
                <i class="el-icon-delete cursor-pointer"></i>
              </div>
            </div>
            <div>
              <div class="change_all_price" v-if="value.itemType==1">
                <div class="subtotal_price_give">
                  有效期：
                  <el-date-picker
                    style="width: 150px"
                    v-model="value.indate"
                    type="date"
                    placeholder="选择日期"
                    value-format="yyyy-MM-dd"
                    :picker-options="giftPickTimeOptions"
                  ></el-date-picker>
                  <div
                    style="margin-left: 160px; color: #3e63dd; cursor: pointer"
                    @click="copyIndate(index)"
                  >
                    批量时间
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="open_details_pay">
        <div>
          <div
            style="
              display: flex;
              flex-direction: row;
              justify-content: flex-end;
            "
          >
            <div></div>
            <div class="order_three">
              <div
                class="open_details_pay_choice_font3"
                @click="cancelChooseGift"
              >
                取消
              </div>
              <div
                class="open_details_pay_choice_font4"
                @click="comfirmChooseGift"
              >
                确定
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
  <!--选择服务规格-->
  <el-dialog
    title="选择产品规格"
    :visible.sync="isProductSpecifications"
    :show-close="false"
    top="7vh"
    width="600px"
    :before-close="handleCloseSpecification"
  >
    <div class="C_open_order_Specifications">
      <div class="C_open_order_Specifications1">
        <div style="display: flex; align-items: center">
          <img
            :src="C_open_order_Specifications.imgurl"
            onerror="this.src='images/default.jpg'"
            class="cashier_open_order_Specifications_img"
          />
        </div>
        <div class="C_open_order_Specifications_font">
          <div class="C_open_order_Specifications_font1">
            {{C_open_order_Specifications.product_name}}
          </div>
          <div
            class="C_open_order_Specifications_font2"
            v-if="C_open_order_Specifications.price"
          >
            ￥{{C_open_order_Specifications.price}}
          </div>
          <div
            class="C_open_order_Specifications_font2"
            v-else="C_open_order_Specifications.realPrice"
          >
            {{C_open_order_Specifications.realPrice}}
          </div>
        </div>
      </div>
      <div class="C_open_order_Specification2">
        <div
          class="C_open_order_Specifications2"
          :id="value.id"
          v-for="(value,index1) in C_open_order_specifications_name"
        >
          <div class="C_open_order_Specifications2_font1">
            <span v-if="value.name">{{value.name}}</span>
            <span v-if="value.title">{{value.title}}</span>
          </div>
          <div class="C_open_order_Specifications2_font2">
            <div
              v-if="value.item"
              class="C_open_order_Specifications2_font3"
              v-for="(todo,index2) in value.item"
              :id="todo.id"
              @click="specificationBtn_product(value,todo,index1,index2)"
              :class="{C_Specifications_choice1_add:todo.is_show}"
            >
              <span v-if="todo.text">{{todo.text}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="cancel-btn" @click="cancelProductPpecifications">
        取消
      </el-button>
      <el-button type="primary" @click="saveProductSpecifications">
        确定
      </el-button>
    </span>
  </el-dialog>
</div>
