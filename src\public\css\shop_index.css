[v-cloak] {
  display: none !important;
}

.shop-comtent {
  width: 100%;
  height: 100vh;
  background: #f6f6f6;
}

.height {
  height: 100%;
}

.shop_left {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-right: 3px solid #f6f6f6;
  background: #fff;
}

.shop-tab {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 2px solid #eee;
  text-align: center;
  height: 60px;
  line-height: 56px;
  background: #fff;
}

.shop-body {
  box-sizing: border-box;
  padding: 0 15px;
  height: calc(100vh - 200px);
  overflow-y: auto;
  /*60+62+106+30+2*/
}

.shop-body::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.shop-body::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.shop-body::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.shop_public {
  box-sizing: border-box;
  padding: 15px;
}

.shop-body-search {
  margin-bottom: 15px;
}
.loadingtip {
  height: 20px;
  line-height: 20px;
  text-align: center;
  margin: 10px 0 5px;
  font-size: 14px;
  color: rgb(77, 76, 76);
}

.shop-body-tab {
  display: flex;
  background: rgba(238, 238, 238, 1);
  box-sizing: border-box;
  padding: 15px 0;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

.shop-body-tab > span {
  flex: 1;
  margin: 0 auto;
}

.shop-body-tab > span > .body-tab-li {
  width: 100%;
}

.body-tab-li {
  text-align: center;
  font-size: 16px;
  cursor: pointer;
}

.el-icon-caret-bottom {
  color: #666;
}

.server-shop-box {
  height: 100%;
  /* height: calc(100vh - 280px); */
  /*overflow: hidden;*/
  /*background: #f6f6f6;*/
}

.server-shop {
  display: flex;
  box-sizing: border-box;
  padding: 10px;
  border: 1px solid transparent;
  border-bottom: 1px solid #eee;
}

.validity_time {
  font-size: 12px;
}

.aging {
  font-size: 14px;
}

.giveAway-faceValue {
  display: flex;
  justify-content: space-between;
}

.giveAway {
  display: inline-block;
  border: 1px solid #fff;
  box-sizing: border-box;
  padding: 5px 10px;
  font-size: 12px;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  border-radius: 25px;
}

.giveAwayActive {
  border-color: #fff;
}

.shopActive {
  border: 1px solid #3e63dd;
  transition: all 0.5s ease-out;
}

.shopImg {
  width: 80px;
  height: 80px;
}

.shopImg > img {
  width: 80px;
  height: 80px;
  display: inline-block;
}

.shop-list {
  width: calc(100% - 80px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 10px;
}

.shop_name {
  font-size: 16px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.server-price,
.server-statu {
  float: right;
}

.fzc9 {
  color: #999;
  font-size: 14px;
}

.shop_right {
  position: relative;
}

.info-details {
  box-sizing: border-box;
  background: #fff;
  padding: 15px;
}

.shop-info {
  height: calc(100vh - 109px);
  overflow: auto;
}

.shop-info::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.shop-info::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.shop-info::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.shop-info-body {
  display: flex;
  margin-bottom: 15px;
}

.detail_ul {
  width: calc(100% - 280px);
  padding-right: 15px;
}

.detail_li {
  padding: 12px;
  font-size: 16px;
  border-bottom: 1px solid #f6f6f6;
}

.label-li {
  color: #999;
  font-size: 16px;
}

.label-name {
  float: right;
}

.shop-bigImg {
  width: 280px;
  /*height: 200px;*/
  height: 100%;
  position: relative;
}

.shop-bigImg img {
  width: 100%;
  height: 100%;
  display: block;
}

.shop-bigImg > .el-carousel__container {
  height: 100%;
}

.bigIndex {
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  height: 25px;
  text-align: center;
  line-height: 25px;
  font-size: 12px;
  color: #fff;
  background: rgba(0, 0, 0, 0.2);
}

.desc {
  box-sizing: border-box;
  padding: 10px 25px;
  line-height: 20px;
  font-family: "Microsoft YaHei", "SimHei", "SimSun";
}

.shopTable {
  box-sizing: border-box;
  margin: 10px 0;
}

.table-title {
  text-align: left;
}

.table-title > th {
  background: #eeeeee;
  font-size: 16px;
}

.table-title > th,
.table-body > td {
  box-sizing: border-box;
  padding: 12px 8px;
}

.table-body > td {
  border-bottom: 1px solid #e5e5e5;
}

.fixed-btn {
  text-align: right;
  border-top: 1px solid #fff;
}

.BindVip {
  display: inline-block;
  height: 48px;
  line-height: 48px;
}

.btn-button {
  padding: 16px 40px;
  cursor: pointer;
}

.obtained {
  background: #7a7a7a;
  color: #fff;
  border-color: #7a7a7a;
}

.obtained:focus,
.obtained:hover {
  color: #fff;
  border-color: #7a7a7a;
  background-color: #7a7a7a;
}

.promotion {
  background: #2b282c;
  border-color: #2b282c;
  color: #fff;
}

.promotion:focus,
.promotion:hover {
  color: #fff;
  border-color: #2b282c;
  background-color: #2b282c;
}

.el-button-group > .el-button:not(:last-child) {
  margin-right: 0px;
}

.editBtn {
  background: #3e63dd;
  color: #fff;
}

/*弹出层*/
.edit_body {
  width: 90%;
  margin: 0 auto;
}

.cancel:focus,
.cancel:hover {
  color: #3e63dd;
  border-color: #dcdfe6;
  background-color: #fff;
}

.edit-li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.edit-li-label {
  display: inline-block;
  width: 70px;
  font-size: 16px;
  color: #333;
}

.edit-box {
  flex: 1;
}

.el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.tip {
  height: 40px;
  line-height: 40px;
  padding-left: 69px;
}

.popover-wrap {
  position: relative;
  font-size: 16px;
}

.popover-title {
  text-align: center;
  margin-bottom: 15px;
}

.cancel-popover {
  position: absolute;
  top: 0;
  right: 15px;
  color: #3e63dd;
  cursor: pointer;
}

.popover-li {
  padding: 10px 0;
  border-bottom: 1px solid #f6f6f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.popover-li > .el-icon-circle-check {
  color: #3e63dd;
  font-size: 20px;
}

.popover-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
}

/*order*/

.order-container {
  /*height: calc(100vh - 60px);*/
  /*overflow:hidden;*/
}

.table-wrap {
  width: 100%;
  box-sizing: border-box;
  /*height: calc(100vh - 150px);*/
  padding: 15px 45px;
}

.order-search {
  margin-bottom: 15px;
}

.orderTable {
  width: 100%;
  margin-bottom: 15px;
}

.orderTable .el-table__body-wrapper {
  /*120 48 55 32 15 30*/
  height: calc(100vh - 240px);
  overflow-y: auto;
}

.orderTable .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.orderTable .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.orderTable
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundCardData .el-table__body-wrapper {
  /*120 48 55 32 15 30*/
  height: calc(100vh - 510px);
  overflow-y: auto;
}

.refundCardData .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundCardData .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.refundCardData
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundheader {
  width: 100%;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  max-height: 140px;
  overflow: hidden;
  overflow-y: auto;
}

.refundheader::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundheader::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.refundheader::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundGoods .el-table__body-wrapper {
  max-height: 160px;
  overflow: hidden;
  overflow-y: auto;
}

.refundGoods .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundGoods .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.refundGoods
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundForm {
  max-height: 420px;
  overflow-y: auto;
}

.refundForm::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundForm::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.refundForm::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.table_date {
  font-size: 14px;
  color: #999999;
}

.seeDetails {
  font-size: 14px;
  color: #3e63dd;
}

/*预约*/
.navigation-tab {
  width: 110px;
  height: calc(100vh - 10px);
  box-sizing: border-box;
  padding: 40px 0;
  background: #2b282c;
  -webkit-border-radius: 0 8px 8px 0;
  -moz-border-radius: 0 8px 8px 0;
  border-radius: 0 8px 8px 0;
  color: #fff;
  margin-top: 5px;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  cursor: pointer;
}

.tab-navigation,
.tab-navigation2 {
  box-sizing: border-box;
  padding: 15px 0;
  text-align: center;
}

.tab-navigation2 {
  font-size: 15px;
}

.tab-liActive {
  background: #3e63dd;
  transition: all 0.3s ease-out;
}

.tabText {
  color: #3e63dd;
}

.reservation-wrap {
  display: flex;
}

.reservation-container {
  overflow: hidden;
  width: calc(100vw - 110px);
  background: #f6f6f6;
}

.add_reservation_card {
  width: 95%;
  margin: 10px auto;
}

.padding {
  box-sizing: border-box;
  padding: 0 20px;
}

.form-wrap,
.form-wrap2 {
  box-sizing: border-box;
  padding: 0 30px;
  background: #fff;
}

.form-wrap2 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.form_block,
.form_block2 {
  width: 100%;
  display: flex;
  align-items: center;
}

.form-label,
.form-label2 {
  width: 80px;
  font-size: 14px;
  /* text-align: right; */
}

.form-label2 {
  align-self: flex-start;
  padding-top: 15px;
}

.add-server {
  display: flex;
  align-items: center;
}

.axb > .add-server:last-child {
  margin-bottom: 20px;
}

.add-server > .el-icon-remove {
  display: inline-block;
  font-size: 24px;
  color: #3e63dd;
  margin-right: 15px;
  cursor: pointer;
}

.server-list {
  flex: 1;
  width: 100%;
}

.form-inner,
.form-inner2 {
  flex: 1;
  box-sizing: border-box;
  /* padding: 15px 12px; */
  padding: 5px 5px;
}

.server-list > .form-inner2 {
  display: flex;
  justify-content: space-between;
  flex: 1;
}

.form-inner2 {
  padding: 15px 0;
}

.form-input {
  width: 350px;
  height: 100%;
  border: none;
  outline: none;
}

.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.server-text {
  color: #3e63dd;
  font-size: 15px;
  cursor: pointer;
}

.addTo {
  box-sizing: border-box;
  color: #3e63dd;
  font-size: 15px;
  cursor: pointer;
}

.reservation-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  box-sizing: border-box;
  padding: 8px 0;
  text-align: center;
}

.arrdate {
  width: 150px !important;
}

.arrdate > .el-input__inner {
  cursor: pointer;
  padding: 0 5px;
  color: #3e63dd;
}

.changeActive {
  color: #3e63dd;
  cursor: pointer;
}
/* 去除商品button按钮默认样式 */
.el-button--default:hover {
  color: #3e63dd;
  border-color: #dcdfe6 !important;
  background-color: #fff !important;
}

/* 服务- 规格 */
.srever_sku-title {
  width: 100%;
  text-align: center;
  position: relative;
}

.el-icon-back {
  position: absolute;
  top: 0;
  left: 20px;
  cursor: pointer;
}

.sku-list {
  height: 300px;
  overflow-y: auto;
}

.sku-list::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.sku-list::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.sku-list::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.sku-list > .sku-item {
  box-sizing: border-box;
  padding: 15px 0;
  border-bottom: 1px solid #f6f6f6;
}

.itemSku,
.itemPrice {
  margin-right: 15px;
}

.cancelPrint {
  text-decoration: line-through;
}

/*查看预约*/
.reservation-table {
  width: 100%;
  margin-bottom: 15px;
}

.reservation-table .el-table__body-wrapper {
  /* 48 55 60 15 32  20*/
  height: calc(100vh - 230px);
  overflow-y: auto;
}

.reservation-table .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.reservation-table .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.reservation-table
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.reservation-table .has-gutter {
  /*display: none;*/
}

.details-server {
  height: 400px;
  overflow-y: auto;
}

.see-reservation {
  background: #fff;
  /*height: calc(100vh - 60px);*/
  /*overflow-y: auto;*/
}

.detailsTable-ul {
  display: flex;
  box-sizing: border-box;
  padding: 10px 0;
  border-bottom: 1px solid #f6f6f6;
}

.detailsTable-li {
  flex: 1;
  box-sizing: border-box;
  padding: 0 15px;
}

.shoperName {
  width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shoper-tip {
  display: inline-block;
  box-sizing: border-box;
  padding: 4px 6px;
  background: #f6f6f6;
  border-radius: 4px;
  font-size: 12px;
}

.margin-bottom {
  margin-bottom: 5px;
}

.reservation-search {
  box-sizing: border-box;
  padding: 0 20px 20px 20px;
}

.details-wrap {
  display: flex;
  border-bottom: 1px solid #f6f6f6;
  box-sizing: border-box;
  padding: 15px 0;
  position: relative;
}

.details-order-status {
  position: absolute;
  top: 15px;
  right: 0;
}

.details-server-wrap {
  flex: 1;
}

.details-label {
  width: 60px;
}

.details-server-info {
  width: 100%;
  margin-bottom: 15px;
}

.details-product {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.details-product-list {
  margin-bottom: 10px;
}

.details-product-list:last-child {
  margin-bottom: 0;
}

.details-technician {
  display: flex;
  justify-content: space-between;
}

/*.el-table thead {*/
/*display: none;*/
/*}*/

/*.details-mask.el-dialog {*/
/*left: 60%;*/
/*height: 100%;*/
/*margin: 0;*/
/*margin-top: 0;*/
/*position: relative;*/
/*}*/

.dialog-title {
  display: flex;
  justify-content: space-between;
}

.notice-mask > .el-dialog__header {
  padding: 0;
}

.dialog-title > .el-dialog__close,
.cancelReservation {
  display: inline-block;
  width: 80px;
  cursor: pointer;
}

.cancelReservation {
  color: #3e63dd;
  text-align: center;
  font-size: 14px;
}

.details-mask .el-dialog__footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 0;
  z-index: 10;
}

.details-mask .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: left;
  box-sizing: border-box;
  padding: 5px 0px 0px 15px;
}

.reservation-price {
  color: #333;
  margin-bottom: 5px;
}

.reservation-all {
  color: #999;
  font-size: 12px;
}

.reservation-group {
  display: flex;
}

.reservationBtn {
  padding: 15px 35px;
  color: #fff;
  text-align: center;
  font-size: 15px;
  cursor: pointer;
}

.save-btn {
  background: #dddddd;
}

.billing-btn {
  background: #3e63dd;
}

/*****/

/*.cancelReservation .el-dialog__footer{*/

/*}*/

.reason-footer {
  box-sizing: border-box;
  padding: 30px 20px;
}

/* 到店预约收款-- 上门预约收款 */

/* 小票打印 */
.print-title {
  width: 100%;
  box-sizing: border-box;
  padding: 15px;
  display: flex;
  background: #f6f6f6;
}

.print-cancel,
.print-confirm {
  width: 50px;
  color: #3e63dd;
}

.print-cancel {
  text-align: left;
  cursor: pointer;
}

.print-confirm {
  text-align: right;
  cursor: pointer;
}

.print-Text {
  flex: 1;
  text-align: center;
  font-size: 16px;
}

.print-main {
  box-sizing: border-box;
  padding: 0 15px;
  padding-bottom: 15px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

.store-name {
  font-size: 18px;
  text-align: center;
  box-sizing: border-box;
  padding-top: 15px;
  font-weight: 600;
}

.print-info {
  max-height: 250px;
  overflow-y: auto;
}

/*.print-border {*/
/*border-bottom: 1px solid #F6F6F6;*/
/*margin-bottom: 15px;*/
/*}*/

.print-scanCode {
  width: 100%;
  text-align: center;
}

.rwm {
  display: inline-block;
  width: 100px;
  margin-bottom: 10px;
}

.print-date-li {
  display: flex;
}

.print-serialNumber {
  width: 30px;
  text-align: left;
}

.print-shop {
  flex: 1;
}

.print-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

/*发卡*/
.cardIssue-mask .el-dialog__header {
}

.cardIssueTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardIssueTitle > .el-icon-arrow-left {
  width: 128px;
}

.cardIssue-text {
  flex: 1;
  text-align: center;
}

.el-popup-parent--hidden {
  padding-right: 0 !important;
}

.serverPriceTable {
  margin-bottom: 30px;
}

.serverPriceTable .PriceTable-tr th,
.serverPriceTable .PriceTable-tr td {
  background: rgb(246, 246, 246);
  color: rgb(51, 51, 51);
  font-size: 15px;
  font-weight: 600;
  box-sizing: border-box;
  padding: 12px 10px;
}

.serverPriceTable .PriceTable-tr td {
  background: #fff;
  border-bottom: 1px solid #f6f6f6;
  font-weight: 400;
}

@media screen and (min-width: 1200px) {
  .reservation-choose-time {
    height: calc(100vh - 600px);
    width: calc(100vw - 1180px);
    overflow: hidden;
  }
}
@media screen and (max-width: 1200px) {
  .reservation-choose-time {
    height: calc(100vh - 320px);
    width: calc(100vw - 450px);
    overflow: hidden;
  }
}

.reservation-choose-time-ul {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  cursor: default;
}

.reservation-choose-time-ul-li {
  display: block;
  margin: 0 10px 2px 0;
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
}

.choose-time-active {
  color: #3e63dd;
  border: 1px solid #c9c9c9;
}

.reservation-choose-time-ul::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  height: 8px;
  overflow: hidden;
}

.reservation-choose-time-ul::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  height: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.reservation-choose-time-ul::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.choose-time-body::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.choose-time-body::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.choose-time-body::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.choose-time-body {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  height: 320px;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: nowrap;
}

.choose-time-label {
  margin-bottom: 4px;
}

.choose-time-item {
  text-align: center;
  width: 20%;
  height: 80px;
  line-height: 80px;
}

.time-item-normal {
  font-weight: bold;
  cursor: pointer;
}

.time-item-active {
  font-weight: bold;
  color: #3e63dd;
}

.time-item-expire {
  cursor: not-allowed;
  color: #cccccc;
}

.time-item-select {
  /*color: #cccccc;*/
  cursor: not-allowed;
  text-decoration: line-through;
}
/* 查看预约列表部分样式 */
.reservation_table_expand {
  font-size: 0;
}
.reservation_table_expand label {
  width: 90px;
  color: #99a9bf;
}
.reservation_table_expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.refundApply {
  margin: 20px -10px;
}

.elementImage img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
