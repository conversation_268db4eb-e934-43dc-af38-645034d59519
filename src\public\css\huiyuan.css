div {
  -moz-user-select: none; /*mozilar*/
  -webkit-user-select: none; /*webkit*/
  -ms-user-select: none; /*IE*/
  user-select: none;
}

/*以上是导航条样式*/

.main-caontainer {
  width: calc(100vw - 110px);
  height: 100vh;
}
.add_member_bg {
  width: 100%;
  padding-top: 10px;
  height: 100%;
  overflow-y: auto;
  background-attachment: fixed;
  margin-bottom: 0;
}
.add_member_bg::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}
.add_member_bg::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.add_member_bg::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #f1f1f1;
}

.main {
  display: flex;
  overflow: hidden;
  /* background: #f1f1f1; */
}

.left {
  height: 100vh;
  overflow: hidden;
}

/* 主体--右侧 */
.main-right {
  flex: 1;
}

.server {
  width: 500px;
  height: calc(100vh - 90px);
  /*overflow: hidden;*/
  /*background-color: yellow;*/
}

.server_chioce {
  width: 580px;
  height: 90px;
  border: 1px solid rgba(255, 255, 255, 1);
}

.server_center {
  width: 240px;
  height: 50px;
  display: flex;
  margin: auto;
  margin-top: 20px;
}

.b_left {
  width: 120px;
  height: 50px;
  border: 1px solid #3e63dd;
  border-radius: 8px 0px 0px 8px;
  text-align: center;
  line-height: 50px;
  color: #3e63dd;
}

.b_left:hover {
  cursor: pointer;
}

.b_right {
  width: 120px;
  height: 50px;
  /*border: 1px solid green;*/
  border: 1px solid #3e63dd;
  border-radius: 0px 8px 8px 0px;
  text-align: center;
  line-height: 50px;
  color: #3e63dd;
}

.b_right {
  cursor: pointer;
}

.server_bg {
  background-color: #3e63dd;
  color: #ffffff;
}

.server_line1 {
  width: 580px;
  height: 2px;
  background: rgba(238, 238, 238, 1);
}

.search_menu {
  width: 540px;
  height: calc(100vh - 202px);
  overflow: hidden;
  /*border: 1px solid red;*/
  margin-top: 16px;
  margin-left: 20px;
}

.search_bor {
  width: 538px;
  height: 50px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 5px;
}

.search_input {
  width: 400px;
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  margin-top: 15px;
  margin-left: 25px;
}

.search_bor img {
  width: 24px;
  height: 26px;
  margin-top: 11px;
  margin-left: 30px;
}

.search_bor img:hover {
  cursor: pointer;
}

.search_label {
  width: 540px;
  /*border: 1px red solid;*/
  float: left;
  overflow: hidden;
}

.search_label ul li {
  float: left;
  overflow: hidden;
  border: 1px solid #999999;
  border-radius: 22px;
  margin-left: 10px;
  margin-top: 12px;
}

.search_label ul li {
  cursor: pointer;
}

.search_label ul li p {
  font-size: 17px;

  font-weight: 400;
  color: #666666;
  display: inline-block;
  padding: 11px 32px 11px 32px;
}

.bg_label {
  background: #3e63dd;
  border: 1px solid #3e63dd !important;
}

/*swiper测试*/
/*.swiper-container {*/
/*width: 600px;*/
/*height: 300px;*/
/*}*/
.search_detail {
  width: 540px;
  margin-top: 16px;

  /*border: 1px solid red;*/
}

.search_detail .search_detail1 {
  cursor: pointer;
}

.search_detail .search_detail1 .serach_detail_info {
  display: flex;
  overflow: hidden;
}

.search_detail .search_detail1 .serach_detail_info img {
  display: inline-block;
  width: 110px;
  height: 100px;
  /*border: 1px solid green;*/
  margin-left: 10px;
  margin-top: 12px;
}

.search_detail .search_detail1 .serach_detail_info .serach_detail_info_font {
  margin-top: 12px;
  margin-left: 14px;
  height: 100px;
  /*border: 1px solid gold;*/
}

.search_detail
  .search_detail1
  .serach_detail_info
  .serach_detail_info_font
  .serach_detail_info_font1 {
  margin-top: 12px;
  font-size: 20px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.search_detail
  .search_detail1
  .serach_detail_info
  .serach_detail_info_font
  .serach_detail_info_font2 {
  margin-top: 32px;
  width: 91px;
  height: 18px;
  font-size: 18px;

  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  /*border: 1px solid red;*/
  display: flex;
  /*overflow: hidden;*/
}

.search_detail .search_detail1 .search_detail_line {
  width: 540px;
  border: 0.5px solid rgba(229, 229, 229, 1);
  margin-top: 12px;
}

.menu_line {
  width: 4px;
  border: 1px solid rgba(238, 238, 238, 1);
  background: rgba(238, 238, 238, 1);
}

.open_details {
  flex: 1;
  height: calc(100vh - 60px);
  /*overflow: auto;*/
}

.open_details_title_font1 {
  width: 120px;
  height: 32px;
  font-size: 22px;
  font-weight: 400;
  color: rgba(0, 0, 0, 1);
  display: inline-block;
  text-align: center;
  /*border: 1px solid red;*/
}

.open_details_border {
  position: relative;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
}

.open_details_title {
  text-align: center;
  font-size: 16px;
  font-weight: 400;
}

.open_details_title_font2 {
  position: absolute;
  top: 15px;
  right: 18px;
  height: 24px;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  line-height: 24px;
  padding: 2px 8px;
  border: 1px solid #3e63dd;
  border-radius: 8px;
  cursor: pointer;
}

.open_details_info {
  overflow: hidden;
  box-sizing: border-box;
  padding: 18px 20px;
}

.search_open {
  height: 48px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 5px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 15px;
  margin-bottom: 18px;
}

.search_opne_input {
  height: 48px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  flex: 1;
}

.search_open img {
  width: 24px;
  height: 26px;
  display: inline-block;
}

.search_open img:hover {
  cursor: pointer;
}

.open_server_name {
}

.open_details_price {
  display: flex;
  align-items: center;
  padding-right: 15px;
}

.open_shop {
  flex: 1;
  box-sizing: border-box;
  box-sizing: border-box;
  padding: 40px 15px;
  border-left: 6px solid #3e63dd;
}

.open_server_name .open_details_price {
  background: #f5f5f5;
  overflow: hidden;
  /*margin-top: 18px*/
}

.open_details_price_bg {
  background: #edf2fe;
}

.open_details_price_line {
  background: #f5f5f5;
  border: 1px solid #f5f5f5;
  width: 4px;
}

.open_details_price_line_bg {
  background: #3e63dd;
  border: 1px solid #3e63dd;
}

.font-weight {
  font-weight: 400;
}

.open_details_price_name,
.open_details_price_num,
.open_details_price_all {
  flex: 1;
}

.open_details_price_name {
  font-size: 20px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.open_details_price_num {
  font-size: 18px;
  font-weight: 400;
  color: black;
}

.open_details_price_num img {
  width: 18px;
  height: 18px;
  margin-top: 4px;
}

.open_details_price_all {
  /*width:182px;*/
  height: 25px;
  font-size: 18px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.open_details_price_all img {
  width: 18px;
  height: 18px;
  margin-top: 4px;
}

.open_details_price_del {
  width: 24px;
  height: 24px;
  margin-top: 30px;
}

.open_details_price_del > img {
  display: inline-block;
}

.open_details_price_del img:hover {
  cursor: pointer;
}

.change_all_price {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
  padding: 0 15px;
  border-left: 6px solid #3e63dd;
}

.change_all_price_line {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  width: 4px;
}

.change_all_price_font1 {
  color: #999999;
}

.change_all_price_font2 {
  font-size: 14px;
}

.change_all_price_font2 img {
  width: 15px;
  height: 15px;
  color: #333333 !important;
  margin-top: 1px;
}

.change_all_price_font2 span {
}

.change_all_price_font3 {
  color: #999999;
  font-size: 14px;
}

.change_all_price_font3:hover {
  cursor: pointer;
}

.chioce_technician {
  display: flex;
  overflow: hidden;
  height: 60px;
  border-left: 6px solid #3e63dd;
}

.chioce_technician_line {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  width: 5px;
}

.chioce_technician_name,
.selective_sales_volume,
.batch {
  flex: 1;
}

.chioce_technician_name {
  height: 58px;
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  line-height: 58px;
}

.selective_sales_volume {
  height: 58px;
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  line-height: 58px;
}

.batch {
  height: 58px;
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  line-height: 58px;
}

.chioce_technician_name_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
}

.chioce_technician_name_font1:hover {
  cursor: pointer;
}

.chioce_technician_name_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.selective_sales_volume_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
}

.selective_sales_volume_font1:hover {
  cursor: pointer;
}

.selective_sales_volume_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.batch_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.batch_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.order_remark {
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  box-sizing: border-box;
  padding: 0 18px;
}

.remark_input {
  font-size: 14px !important;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
}

.order_remark_font {
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}

.order_remark_input {
  height: 59px;
  box-sizing: border-box;
  padding: 0 15px;
  border-top: 1px solid #e5e5e5;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
}

.remark_input {
  width: 100%;
  height: 100%;
  font-size: 18px;
}

.open_details_pay {
}

.chioce_Discount {
  height: 58px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding-left: 15px;
  border: 1px solid #e5e5e5;
}

.chioce_Discount_font1 {
  height: 23px;
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chioce_Discount_font0 {
  font-size: 14px;
}

.chioce_Discount_font2 {
  width: 60px;
  height: 19px;
  font-size: 20px;
  font-weight: 400;
  color: #3e63dd;
}

.chioce_Discount_font3 {
  width: 20px;
  height: 20px;
}

.open_details_pay_choice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  padding-left: 15px;
}

.open_details_pay_choice_font4,
.open_details_pay_choice_font2,
.open_details_pay_choice_font3 {
  box-sizing: border-box;
  padding: 20px 50px;
  font-size: 20px;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
}

.open_details_pay_choice_font1 {
  color: rgba(51, 51, 51, 1);
  font-size: 20px;
}

.open_details_pay_choice_font2 {
  background: rgba(122, 122, 122, 1);
}

.open_details_pay_choice_font3 {
  background: rgba(43, 40, 44, 1);
}

.open_details_pay_choice_font4 {
  background: #3e63dd;
}

.order_three {
  display: flex;
}

/*模态框 选择技师*/
.xuanze_jishi {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 24px;
  margin-bottom: 24px;
}

.over_save {
  display: flex;
  cursor: pointer;
}

.over_save_over {
  flex: 1;
  font-size: 21px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 0px 0px 0px 7px;
  text-align: center;
  padding: 18px 0px 18px 0px;
}

.over_save_save {
  flex: 1;
  font-size: 21px;
  font-weight: 400;
  background: #3e63dd;
  border-radius: 0px 0px 7px 0px;
  text-align: center;
  padding: 18px 0px 18px 0px;
  color: rgba(255, 255, 255, 1);
}

.xuazne_xiaoshou {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 15px;
}

.qudan_font {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.detail_say {
  background: rgba(246, 246, 246, 1);
  border-radius: 4px;
  padding: 10px 30px 10px 30px;
}

.detail_ul {
  display: flex;
  justify-content: space-between;
}

.detail_ul li {
  font-size: 17px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

.qu_dan {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 48px;
}

.shops {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
}

.shops_num {
  /*flex:1;*/
  padding: 12px 0px 12px 30px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.server_name {
  /*flex:1;*/
  padding: 21px 0px 21px 50px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  line-height: 38px;
}

.shops_name {
  /*flex:1;*/
  padding: 21px 0px 21px 20px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.shops_name_font {
  padding: 5px 0px 0px 0px;
}

.shops_servername {
  /*flex:1;*/
  padding: 21px 30px 21px 0px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.shops_servername_font {
  padding: 5px 0px 0px 0px;
}

.shops_price {
  /*flex:1;*/
  padding: 21px 0px 21px 0px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  line-height: 38px;
}

.shops_get_money {
  /*flex: 1;*/
  margin: 15px 5px 15px 0px;
  padding: 10px 17px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  background: #3e63dd;
  border-radius: 4px;
  text-align: center;
  line-height: 38px;
  cursor: pointer;
}

/*以下是会员的样式*/
/*添加会员 ， */
.huiyaun_add_huiyuan {
  /* padding-left: 30px; */
  width: 95%;
  margin: 0 auto;
  background-color: rgb(253, 253, 253);
}

.border-bottom {
  /* border-bottom: 7px solid #F6F6F6; */
  width: 60%;
}

.padding20 {
  box-sizing: border-box;
  padding: 0 20px;
}

.huiyaun_add_huiyuan_main {
  /* padding-top: 10px; */
  border: 1px solid white;
}

.vip-main-form {
  max-height: calc(100vh - 100px);
  overflow: auto;
}

.vip-main-form::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vip-main-form::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.vip-main-form::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.huiyuan_info_font {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.huiyaun_info_one {
  display: flex;
  align-items: center;
  /* border-bottom: 1px solid #E5E5E5; */
  padding: 5px 0px;
}

.huiyuan_info_font {
  width: 80px;
}

.huiyuan_info_font1 {
  flex: 1;
}

.huiyuan_info_font8 {
  font-size: 14px;
  white-space: nowrap;
  width: 300px;
  /*width: 20%;*/
}

.member_input input {
  width: 350px;
}

.huiyuan_name_input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_name_input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_name_input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_name_input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_info_font2 {
  flex: 1;
  padding: 5px 0px 0px 0px;
  cursor: pointer;
}

.huiyuan_info_font3 {
  flex: 1;
  padding: 5px 0px 0px 0px;
  cursor: pointer;
}

.huiyaun_info_one1 {
  display: flex;
  border-bottom: 7px solid #e5e5e5;
  padding: 15px 0px;
}

.huiyuan_info_font4 {
  flex: 1;

  /*cursor: pointer;*/
}

.huiyuan_info_font5 {
  flex: 1;
  padding: 1px 0px 0px 0px;
  display: flex;
}

.vip_sex {
  border: 1px solid rgba(229, 229, 229, 1);
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px !important;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}

.vip_sex_show {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  color: white;
}

.vip_sex_none {
}

.huiyaun_info_one2 {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 0px;
}

.huiyuan_info_font6 {
  flex: 1;
}

.huiyuan_info_font7 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  width: 80px;
  padding: 12px 0px 0px 0px;
}

.huiyaun_info_one3 {
  display: flex;
  /* border-bottom: 1px solid #E5E5E5; */
  padding: 5px 0px;
}

.huiyaun_info_one4 {
  /*padding: 80px;*/
  box-sizing: border-box;
  text-align: center;
  white-space: nowrap;
  width: 60%;
}

.huiyuan_info_anniu {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  padding-top: 15px;
  width: 250px;
}

.huiyuan_info_font10 {
  border-radius: 4px;
  padding: 12px 40px;
  border: 1px solid #3e63dd;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.huiyuan_info_font9 {
  border-radius: 4px;
  padding: 12px 40px;
  border: 1px solid #3e63dd;
  font-size: 14px;
  font-weight: 400;
  background: #3e63dd;
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
}

.vip_save_show {
  background: #3e63dd;
  color: rgba(255, 255, 255, 1);
}

.vip_save_none {
  background: rgba(255, 255, 255, 1);
  color: #3e63dd;
}

/*以下是查看会员的css*/

.hy_chakan_kaidan {
  position: relative;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
  height: 60px;
  width: 100%;
}

.hy_chakan_kaidan_font0 {
  text-align: center;
  line-height: 60px;
  width: 100%;
}

.hy_chakan_kaidan_font1 {
  position: absolute;
  top: 14px;
  right: 15px;
  box-sizing: border-box;
  padding: 8px 20px;
  border: 1px solid #3e63dd;
  border-radius: 4px;
  font-size: 14px;
  color: #3e63dd;
  cursor: pointer;
}

.hy_chakan_kandan_sousuo {
  width: 100%;
  box-sizing: border-box;
  padding: 15px 40px;
}

.hy_info_touxiang {
  width: 60px;
  height: 60px;
}

.hy_chakan_table {
  padding: 0 40px;
}

.member_name {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.membershipDetail_box {
  margin-top: 7vh;
}

.vipTable {
  margin-bottom: 15px;
}

.vipTable .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: calc(100vh - 270px);
  overflow-y: auto;
}

.vipTable .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vipTable .el-table__body-wrapper::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.vipTable .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.dialog-title {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 15px 25px;
  border-bottom: 1px solid #ebeef5;
}

.del_shaixuan {
  font-size: 16px;
  color: #3e63dd;
  font-weight: 400;
  cursor: pointer;
}

.shaixuan_titel {
  font-size: 16px;
  color: black;
  font-weight: 400;
}

.shaixuan_wancheng {
  font-size: 16px;
  color: #3e63dd;
  font-weight: 400;
  cursor: pointer;
}

.memberScreening {
  height: calc(100vh - 300px);
  overflow: auto;
}

.memberScreening::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}
.memberScreening::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.memberScreening::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #f6f6f6;
}

.memberScreening .el-collapse {
  box-sizing: border-box;
  padding: 10px 25px;
}

.screening-list {
}

.list-tabel {
  box-sizing: border-box;
  padding: 15px 25px;
  border-bottom: 5px solid #f6f6f6;
}

.list-tabel-title {
  margin-bottom: 15px;
}

.hy_shaixuan1 {
  display: flex;
  box-sizing: border-box;
  flex-wrap: wrap;
  padding-bottom: 15px;
}

.hy_shaixuan1_font {
  box-sizing: border-box;
  padding: 8px 12px;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 1px solid #999999;
  border-radius: 4px;
  font-size: 14px;
  color: #666666;
  cursor: pointer;
}

.memberScreening .el-collapse-item__content {
  padding-bottom: 0;
  line-height: 1;
}

.shaixuan1_add {
  background: #3e63dd;
  color: white;
  border: 1px solid #3e63dd;
}

.element-input {
  margin-right: 15px;
}

.el-input.hy_shaixuan1_input {
  width: 150px;
  color: #666666;
}

.hy_model1_title {
  height: 60px;
  display: flex;
  /*justify-content: space-between;*/
  font-size: 16px;
  color: rgba(43, 40, 44, 1);
  padding: 0px 30px !important;
  line-height: 60px;
}

.el-dialog__header {
  padding: 0px 0px 0px 0px !important;
}

.vip_model1_imgs {
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

.vip_model1_info1 {
  padding-left: 20px;
}

.vip_model1_info1_font1 {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  padding-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 150px;
}

.vip_model1_info1_font2 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
}

.vip_model1_info1_font3 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.el-dialog__body {
  padding: 0px 0px !important;
}

.vip_model1_info2_font1 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  padding: 55px 10px 0px 0px;
}

.vip_model1_info2_font2 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_info3_font1 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  padding: 47px 10px 0px 0px;
}

.vip_model1_info4_font4 {
  display: flex;
  /* justify-content: space-between; */
  padding-bottom: 10px;
}

.vip_model1_info4_font5 {
  display: flex;
  /* justify-content: space-between; */
  /* padding-top: 10px; */
}

.vip_model1_info4_font1 {
  padding: 5px 15px;
  color: rgba(255, 255, 255, 1);
  border: 1px solid #3e63dd;
  border-radius: 5px;
  background: #3e63dd;
  cursor: pointer;
  margin-right: 10px;
}

.vip_model1_info4_font2 {
  padding: 5px 10px;
  color: rgba(255, 255, 255, 1);
  border: 1px solid #3e63dd;
  border-radius: 5px;
  background: #3e63dd;
  cursor: pointer;
  margin-right: 10px;
}

.vip_model1_info4_font3 {
  padding: 5px 10px;
  color: #e2c492;
  border: 1px solid #333333;
  border-radius: 5px;
  background: #333333;
  cursor: pointer;
  margin-right: 10px;
}

.vip_model1_info5_font1 {
  display: flex;
  justify-content: space-around;
  margin: 15px 0;
  padding: 5px 0px;
  width: 100%;
  background: #f6f6f6;
}

.vip_model1_info5_font2 {
  display: flex;
  justify-content: space-between;
  margin: 5px 0px;
}

.vip_model1_info5_font3 {
}

.vip_model1_info5_font4 {
  padding: 5px 0px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_info5_font5 {
  padding: 5px 0px;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  text-align: center;
}

.vip_model1_info5_font6 {
  height: 40px;
  border: 0.5px solid rgba(229, 229, 229, 1);
  margin: 5px 10px;
}

.vip_model1_info5_font7 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_info5_font8 {
  padding: 5px 0px;
}

.vip_model1_info5_font9 {
  padding: 5px 0px;
}

.vip_model1_info5_font10 {
  text-align: center;
  width: 185px;
  padding: 20px 10px;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.vip_model1_info5_font10:hover {
  background: #fff;
}

.vip_model1_info5_font11 {
  padding: 5px 0px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_info5_font12 {
  padding: 5px 0px;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
}

.vip_model1_info5_font13 {
  margin: 5px 0px;
  padding: 5px 0px 5px 10px;
  width: 165px !important;
}

.vip_model1_info5_font14 {
  padding: 5px 0px 5px 0;
  font-size: 14px;
  font-weight: 400;
}

.vip_model1_caozuo {
  width: 100%;
  margin: auto;
}

.vip_model1_caozuo .el-tabs__item {
  /* font-size: 20px !important; */
}

.vip_model1_vip_title_font1 {
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-left: 4px solid rgba(204, 204, 204, 1);
}

.vip_model1_vip_title_font2 {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  padding-left: 10px;
}

.vip_model1_vip_title_font3 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  padding-right: 30px;
  cursor: pointer;
}

.vip_model1_vip_title_font4 {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  border: 1px solid #3e63dd;
  border-radius: 4px;
  margin-right: 10px;
}

.vip_model1_vip_title_font5 {
  display: flex;
  margin: 20px 0px 10px 5px;
}

.vip_model1_vip_title_font6 {
  display: flex;
  justify-content: space-between;
  margin: 20px 0px 0px 0px;
}

@media screen and (max-width: 1200px) {
  .vip_model1_vip_title_font7 {
    /* height: 110px; */
    overflow: hidden;
    overflow-y: auto;
    flex: 0.45;
  }
  .trade_recond {
    /* height: 170px; */
    overflow: hidden;
    overflow-y: auto;
  }
}

@media screen and (min-width: 1201px) {
  .vip_model1_vip_title_font7 {
    overflow: hidden;
    overflow-y: auto;
    flex: 0.45;
  }
  .trade_recond .el-table__body-wrapper {
    overflow: hidden;
    overflow-y: auto;
    /* height: 440px; */
  }
}

.vip_model1_vip_title_font7::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vip_model1_vip_title_font7::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.vip_model1_vip_title_font7::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.trade_recond .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.trade_recond .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.trade_recond
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/*交易记录的滚动条*/
.trade_recond::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.trade_recond::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.trade_recond::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/*服务日志内容*/
.service_log_content {
  padding-left: 20px;
}

.service_log_img {
  margin-top: 14px;
  margin-left: 20px;
}

.service_log_img img {
  width: 50px;
  height: 50px;
  margin-right: 5px;
}

.vip_model1_vip_title_font8 {
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-left: 4px solid rgba(204, 204, 204, 1);
}

.vip_model1_vip_title_font9 {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  padding-left: 10px;
}

.vip_model1_vip_title_font10 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  padding-right: 30px;
  cursor: pointer;
}

.vip_model1_vip_title_font11 {
  display: flex;
  margin: 20px 0px 0px 0px;
  padding-top: 20px;
  border-top: 1px solid rgba(229, 229, 229, 1);
}

.vip_model1_vip_title_font12 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_vip_title_font13 {
  padding-bottom: 10px;
}

.vip_model1_vip_title_font14 {
  padding-left: 30px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  width: 65%;
}

.vip_model1_vip_title_font15 {
  padding-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vip_model1_vip_title_font16 {
  flex: 0.45;
}

.vip_model1_vip_title_font17 {
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-left: 4px solid rgba(204, 204, 204, 1);
}

.vip_model1_vip_title_font18 {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  padding-left: 10px;
}

.vip_model1_vip_title_font19 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  padding-right: 30px;
  cursor: pointer;
}

.vip_model1_vip_title_font20 {
  margin: 20px 0px 0px 0px;
  padding-top: 20px;
  border-top: 1px solid rgba(229, 229, 229, 1);
}

.vip_model1_vip_title_font21 {
  display: flex;
}

.vip_model1_vip_title_font22 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  padding-right: 20px;
}

.vip_model1_vip_title_font23 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_vip_title_font24 {
  padding: 20px 0px 0px 0px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_vip_title_font26 {
  padding: 10px 0px 0px 0px;
}

.vip_model1_vip_title_font27 {
  width: 50px;
  height: 50px;
  padding: 10px;
}

.vip_model1_info_fuwu_hight {
  height: calc(100vh - 510px);
  overflow: auto;
}

.vip_model1_info_fuwu_hight::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vip_model1_info_fuwu_hight::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.vip_model1_info_fuwu_hight::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.vip_model1_info_fuwu {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid rgba(229, 229, 229, 1);
}

.vip_model1_info_fuwu_font1 {
  display: flex;
  margin: 10px 10px;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.vip_model1_info_fuwu_font2 {
  display: flex;
}

.vip_model1_info_fuwu_font3 {
  width: 50px;
  height: 50px;
}

.vip_model1_info_fuwu_font4 {
  padding: 5px 10px;
}

.vip_model1_info_fuwu_font5 {
  font-size: 16px;
  font-weight: 400;
  color: black;
  margin-bottom: 10px;
}

.vip_model1_info_fuwu_font6 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.vip_model1_info_fuwu_font7 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  padding: 20px 10px;
}

.vip_model1_info_fuwu_font8 {
  margin: 10px 10px;
  padding-top: 30px;
}

.vip_model1_info_fuwu_font9 {
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  font-size: 14px;
  font-weight: 400;
  color: black;
  background: #999999;
}

/*以下是付费会员模态框*/

.saoma_chongzhi {
  width: 400px;
  height: 60px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.bt_saoma {
  border: 1px solid #3e63dd;
  color: #3e63dd;
  border-radius: 5px;
  height: 40px;
  line-height: 40px;
  padding: 0px 8px 0px 8px;
  cursor: pointer;
}

.chongzhi_view {
  font-size: 16px;
  font-weight: 400;
  color: #3e63dd;
}

.chonngzhi_vip_all {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #e5e5e5;
}

.chongzhi_vip {
  flex: 0.5;
  display: flex;
}

.chongzhi_vip_info {
  width: 100px;
  height: 100px;
}

.chongzhi_vip_info1 {
  padding: 10px 0px 10px 40px;
}

.chongzhi_vip_info_font1 {
  font-size: 16px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  padding-bottom: 15px;
}

.chongzhi_vip_info_font2 {
  width: 200px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  padding-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.chongzhika_info {
}

.chongzhi_vip_info_font3 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chongzhi_vip_info_font4 {
  flex: 0.5;
  font-size: 20px !important;
  text-align: right !important;
  padding-right: 3px;
  line-height: 90px;
  cursor: pointer;
}

.chaongzhika_name_money {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
}

.chongzhika_name_font {
  width: 150px;
  font-size: 16px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  padding: 30px 0px 30px 24px;
}

.chongzhika_money_font0 {
  width: 250px;
  display: flex;
}

.chongzhika_money_font1 {
  flex: 0.45;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  padding: 46px 0px 0px 0px;
}

.chongzhika_money_font2 {
  flex: 0.45;
  padding: 20px 0px 0px 0px;
}

.chongzhika_money_font3 {
  font-size: 16px;
  font-weight: 400;
  color: #3e63dd;
  padding-bottom: 10px;
}

.chongzhika_money_font4 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chongzhika_money_font5 {
  flex: 0.1;
  line-height: 80px;
  font-size: 28px !important;
  cursor: pointer;
}

.chioce_paynum {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  padding: 15px 0px 15px 25px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.chongzhi_pay_num {
  display: flex;
  justify-content: space-between;
  padding: 15px 0px 15px 25px;
  border-bottom: 0.5px solid #e5e5e5;
}

.chioce_paynum_font1 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chioce_paynum_font2 {
  border: 0;
  background: 0;
  outline: none;
  color: #3e63dd !important;
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  cursor: pointer;
}

.vip_pay_cengson {
  padding: 15px 0px 15px 25px;
  border-bottom: 2px solid #e5e5e5;
}

.vip_pay_cengson_font1 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.vip_pay_cengson_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.vip_xuanze_xiaoshou {
  padding: 15px 0px 15px 25px;
  border-bottom: 0.5px solid #e5e5e5;
}

.vip_xuanze_xiaoshou1 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  cursor: pointer;
}

.vip_xuanze_xiaoshou2 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.vip_xuanze_xiaoshou3 {
}

.vip_add_zengson {
  padding: 15px 0px 15px 25px;
  border-bottom: 0.5px solid #e5e5e5;
  cursor: pointer;
}

.add_zengson {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
}

/*收款的样式*/

.chongzhi_vip_info_img {
  width: 100px;
  height: 100px;
}

.chongzhi_shoukuan {
}

.chongzhi_shoukuan_font {
  text-align: center;
  width: 200px;
  flex: 1;
  padding: 20px 0px;
  font-size: 18px;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
  background: #3e63dd;
}

/*支付二维码*/

.saoma_weweima {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.zhifu_weweima {
  text-align: center;
}

.zhifu_weweima_img {
  width: 400px;
  height: 400px;
}

/*选择充值金额*/

.chongzhi_chongzhi_jine {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 24px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.chongzhi_jine_mian {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  height: calc(100vh - 400px);
}

.chongzhi_danka {
  width: 180px;
  height: 100px;
  /*padding: 30px 80px 30px 30px;*/
  border: 1px solid rgba(229, 229, 229, 1);
  margin-left: 10px;
  margin-bottom: 10px;
}

.danka_get {
  font-size: 24px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 25px 0px 5px 10px;
}

.danka_post {
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  padding: 0px 0px 0px 10px;
}

/*充值页面添加服务样式*/

.tianjia_fuwu {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
}

.tianjia_fuwu_search {
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  padding: 10px 5px 10px 5px;
  margin-top: 10px;
}

.tianjia_fuwu_input {
  border: 0;
  outline: none;
  background: 0;
}

.tianjia_fuwu_mian {
  display: flex;
  margin-top: 10px;
  height: calc(100vh - 600px);
}

.fuwu_biaoti {
  width: 20%;
}

.fuwu_biaoti_chioce {
  width: 79%;
}

.tianjia_fuwu_font {
  font-size: 14px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
  padding: 10px 10px 10px 10px;
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
  cursor: pointer;
}

.tianjia_fuwu_font0 {
  background: #ebdcf2;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  padding: 10px 10px 10px 10px;
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
  cursor: pointer;
}

.fuwu_biaoti_line {
  /*width: %;*/
  border: 0.5px solid rgba(221, 221, 221, 1);
}

.fuwu_biaoti_chioce {
}

.fuwu_biaoti_chioce_bottom {
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
}

.server_biaoti_name_font {
  height: 30px;
  line-height: 30px;
  padding: 10px 0px 10px 0px;
  display: flex;
  justify-content: space-between;
}

.server_biaoti_name_font1 {
  width: 40px;
  height: 40px;
  margin-top: -3px;
}

.server_biaoti_name_font2 {
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.server_biaoti_name_font3 {
  font-size: 16px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  text-align: right;
}

.cz_open_details_info {
  padding: 20px 25px;
}

.hy_open_details_num {
  height: 50px;
  line-height: 70px;
  font-size: 14px;
  font-weight: 400;
  color: black;
  margin-left: 20px;
}

.hy_span1 {
  padding: 4px 10px 4px 10px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.hy_span2 {
  padding: 4px 10px 4px 10px;
  border: 1px solid rgba(229, 229, 229, 1);
  border-left: none;
  border-right: none;
}

.hy_span3 {
  cursor: pointer;
  padding: 4px 8px 4px 8px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(229, 229, 229, 1);
}

.hy_span4 {
}

.hy_model2_fufeihuiyuan {
}

.hy_model2_fufei_font1 {
  display: flex;
  justify-content: space-between;
  padding: 20px 25px;
  background: rgba(249, 249, 249, 1);
  border-bottom: 0.5px solid rgba(229, 229, 229, 1);
}

.hy_model2_fufei_font2 {
}

.hy_model2_fufei_font4 {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  padding-bottom: 8px;
}

.hy_model2_fufei_font5 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.hy_model2_fufei_font3 {
}

.hy_model2_fufei_font6 {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  padding-bottom: 15px;
}

.hy_model2_fufei_font_lj {
  text-align: right;
  font-size: 20px !important;
  font-weight: 400;
  color: #999999;
  cursor: pointer;
}

.hy_model2_fufei_font7 {
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid rgba(229, 229, 229, 1);
}

.hy_model2_fufei_font8 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  min-width: 90px;
}

.vip-model2_server_name {
}

.vip-model2_server_name_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  padding: 15px 25px;
}

.vip-model2_server_name_font2 {
  height: calc(100vh - 831px);
  overflow: auto;
  padding: 0px 25px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.vip-model2_server_name_font3 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 70px;
}

.vip-model2_server_name_font4 {
  line-height: 70px;
}

.vip-model2_server_name_font5 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
}

.vip-model2_server_name_font6 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
}

.vip-model2_server_name_font7 {
  font-size: 14px !important;
  font-weight: 400 !important;
  color: #999999 !important;
}

.vip_model2_left {
  height: calc(100vh - 60px);
  padding: 20px 20px;
}

.vip_model2_top {
  width: 460px;
  height: 247px;
  background: url("../images/huiyuanzhuanxiangka_bg.png");
  background-size: cover;
}

.vip_model2_top_font1 {
  font-size: 18px;
  font-weight: 400;
  color: rgba(245, 212, 156, 1);
  text-align: center;
  padding-top: 90px;
}

.vip_model2_top_font2 {
  font-size: 16px;
  font-weight: 400;
  color: rgba(245, 212, 156, 1);
  text-align: center;
  padding-top: 20px;
}

.vip_model2_mid {
  display: flex;
  justify-content: space-between;
  padding: 40px 60px;
}

.vip_model2_mid1 {
  text-align: center;
}

.vip_model2_mid1_font1 {
  width: 40px;
  height: 40px;
  padding-bottom: 20px;
}

.vip_model2_mid1_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

.vip_model2_but {
  display: flex;
  justify-content: space-between;
  padding: 40px 60px;
}

.vip_model2_foot {
  padding: 40px 60px;
}

.vip_model2_foot1 {
  width: 70px;
  text-align: center;
}

/*model2的优惠权益*/
.xuanze_qunayi_font0 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 20px 0px 20px 30px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_qunayi_font1 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 200px;
}

.xuanze_qunayi_font2 {
  padding: 20px 0px 20px 30px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.xuanze_qunayi_font3 {
  padding: 20px 20px 20px 30px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.hy_model2_user_qianyi {
  padding: 20px 20px 10px !important;
}

.xuanze_jishi_search i {
  font-size: 18px;
}

.hy_model1_Editing_info {
  height: 60px;
  line-height: 60px;
  /*text-align: center;*/
  padding-left: 30px;
  font-size: 18px;
  font-weight: 500;
}

.vip_info1 {
  padding: 20px 20px;
  border-bottom: 1px solid #ebebeb;
  display: flex;
}

.vip_info2 {
  padding: 20px 20px;
  border-bottom: 1px solid #ebebeb;
  display: flex;
  justify-content: space-between;
}

.vip_info1_font1 {
  font-size: 14px;
  font-weight: 400;
  color: black;
  min-width: 60px;
  padding-top: 5px;
}

.vip_info1_font2 {
  margin-left: 40px;
  border: 0;
  background: 0;
  outline: none;
  width: 100%;
}

.vip_info1_font3 {
  margin-left: 40px;
  border: 0;
  background: 0;
  outline: none;
  width: 100%;
}

.vip_info1_font4 {
  margin-left: 40px;
  border: 0;
  background: 0;
  outline: none;
  width: 100%;
}

.vip_info1_same1::-webkit-input-placeholder {
  color: #999999;
  font-size: 14px;
  font-weight: 400;
}

/* Mozilla Firefox 4 to 18 */
.vip_info1_same1:-moz-placeholder {
  color: #999999;
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

/* Mozilla Firefox 19+ */
.vip_info1_same1::-moz-placeholder {
  color: #999999;
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

/* Internet Explorer 10+ */
.vip_info1_same1:-ms-input-placeholder {
  color: #999999;
  font-size: 14px;
  font-weight: 400;
}

.vip_info1_font5 {
  font-size: 14px;
  font-weight: 400;
  color: black;
  min-width: 60px;
  padding-top: 12px;
}

.vip_info3 {
  padding: 10px 20px;
  border-bottom: 1px solid #ebebeb;
  display: flex;
}

.vip_info3_font1 {
  margin-left: 40px;
}

.vip_info4 {
  padding: 10px 20px;
  border-top: 1px solid #ebebeb;
  display: flex;
  border-bottom: 1px solid #ebebeb;
}

.vip_info4_font1 {
  margin-left: 40px;
}

.vip_info1_font6 {
  margin-left: 40px;
  border: 0;
  background: 0;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.vip_info1_font7 {
  padding: 2px 5px;
  background: #f6f6f6;
  color: #666666;
  cursor: pointer;
}

.vip_info1_font8 {
  padding: 2px 5px;
  margin-left: 10px;
  background: #f6f6f6;
  color: #666666;
  cursor: pointer;
}

.model1_sex_class {
  background: #3e63dd !important;
  color: white !important;
}

.vip_model1_bangding_class {
  height: 60px;
  line-height: 60px;
  padding-left: 20px;
}

.hy_model1_shitika {
  padding: 10px 60px 0px 60px;
}

.hy_model1_shitika1 {
  display: flex;
  padding: 20px 0px;
}

.hy_model1_shitika1_font1 {
  font-size: 14px;
  font-weight: 400;
  color: black;
  width: 100px;
  line-height: 40px;
}

.hy_model1_shitika1_font2 {
  margin-left: 10px;
  width: 100%;
}

.hy_model1_shitika2 {
  display: flex;
  padding: 20px 0px;
}

.hy_model1_shitika2_font1 {
  font-size: 14px;
  font-weight: 400;
  color: black;
  width: 100px;
  line-height: 40px;
}

.hy_model1_shitika2_font2 {
  margin-left: 10px;
  width: 100%;
}

/*会员详情里面的开单页面css有的样式复用了*/

.hy_model1_title_cz {
  height: 60px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  padding: 0px 30px !important;
  line-height: 60px;
}

.iconiconfontyoujiantou {
  font-size: 16px;
  font-weight: 400;
}

.hy_model1_cz_top1 {
  font-size: 16px;
  font-weight: 400;
  color: #000000;
}

.vip_model3_left1 {
  width: 460px;
  height: 92px;
  background: url("../images/chongzhika1_bg.png") no-repeat;
  background-size: cover;
}

.vip_model3_left1_fon1 {
  font-size: 16px;
  font-weight: 400;
  color: #ffffff;
  padding: 20px 20px;
}

.vip_model3_left1_fon2 {
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
  padding: 10px 20px;
}

.vip_model3_left2 {
  border-left: 4px solid #3e63dd;
  margin: 20px 0px;
  padding-left: 10px;
  color: #333333;
  font-size: 14px;
  font-weight: 400;
}

.vip_model3_left3 {
  padding: 20px 10px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
}

.vip_model3_left3_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}

.vip_model3_left3_font2 {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.vip_model3_left4 {
  padding: 20px 10px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
}

.vip_model3_left4_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}

.vip_model3_left4_font2 {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.vip_model3_left5 {
  padding: 20px 10px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
}

.vip_model3_left5_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}

.vip_model3_left5_font2 {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.vip_model3_left6 {
  padding: 20px 10px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
}

.vip_model3_left6_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}

.vip_model3_left6_font2 {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.vip_model3_left7 {
  padding: 20px 10px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
}

.vip_model3_left7_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}

.vip_model3_left7_font2 {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.vip_model3_left8 {
  padding: 20px 10px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
}

.vip_model3_left8_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}

.vip_model3_left8_font2 {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.vip_model3_left9 {
  padding: 20px 10px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
}

.vip_model3_left10 {
  padding: 20px 10px;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}

/*会员model1的会员卡点击后页面css*/

.hy_model1_huiyuanka {
  width: 100%;
  padding: 20px 30px;
}

.hy_model4_top {
  font-size: 16px;
  color: #3e63dd;
  font-weight: 400;
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.hy_model4_mid::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.hy_model4_mid::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.hy_model4_mid::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.hy_model4_mid {
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
  max-height: calc(100vh - 180px);
  overflow-y: auto;
  gap: 10px;
  box-sizing: border-box;
  padding: 20px;
}

.hy_model4_top_font1 {
  border: 1px solid #3e63dd;
  padding: 5px 15px;
  color: #3e63dd;
  font-weight: 400;
  font-size: 14px;
  border-radius: 5px 0px 0px 5px;
}

.hy_model4_top_font2 {
  border: 1px solid #3e63dd;
  padding: 5px 15px;
  color: #3e63dd;
  font-weight: 400;
  font-size: 14px;
  border-radius: 0px 5px 5px 0px;
}

.model4_top_add {
  background: #3e63dd !important;
  color: #ffffff !important;
}

.hy_model4_youxiao_bg_active {
  /*height: 100%;*/
  border: 2px solid;
  border-image: linear-gradient(#15abff, #15abff) 30 30;
  /*border-image: linear-gradient(90deg, #2286ff 0%, #fff012 20%, #fc1311 30%, #2286ff 40%, #fff012 50%, #fc1311 60%, #2286ff 70%, #fff012 80%, #fc1311 90%, #2286ff 100%) 30 30;*/
  border-radius: 0px 5px 5px 0px;
  padding: 16px;
}

.hy_model4_youxiao_bg {
  cursor: pointer;
  border-radius: 0.375rem;
  padding: 16px;
  background: #fff;
  --un-shadow: var(--un-shadow-inset) 0 4px 6px -1px var(--un-shadow-color, rgb(0
            0 0 / 0.1)),
    var(--un-shadow-inset) 0 2px 4px -2px var(--un-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow),
    var(--un-shadow);
  border: 1px solid #e5e5e5;
}

.hy_model4_youxiao_bg:hover {
  background: #f5f7fa;
}

.hy_model4_youxiao_bg1 {
  width: 300px;
}

.hy_model4_youxiao_bg2 {
  width: 300px;
}

.hy_youxiao1_font1 {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 4px;
  /*padding: 10px 0px 5px 10px;*/
}

.hy_youxiao1_font2 {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
  /*padding: 2px 10px;*/
  color: #ffffff;
}

.hy_youxiao1_font3 {
  font-weight: 400;
  margin-bottom: 5px;
  /*padding: 2px 10px;*/
}

.hy_youxiao1_font4 {
  font-weight: 400;
  /*padding: 2px 10px;*/
}

.add_vip_title {
  height: 340px;
}

/*会员详情添加标签页面*/
.add_vip_title2_font2_input::-webkit-input-placeholder {
  /* WebKit browsers */
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}

.add_vip_title2_font2_input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}

.add_vip_title2_font2_input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}

.add_vip_title2_font2_input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}

.add_vip_title1 {
  width: 100%;
  height: 30px;
  /*background: #F6F6F6;*/
  /*border-bottom: 1px solid #E5E5E5;*/
}

.add_vip_title2 {
  /* padding: 0px 0px 0px 30px; */
  display: flex;
  box-sizing: content-box;
  width: 300px;
  /*border: 1px solid #e5e5e5;*/
  /* padding-left: 10px; */
  margin-left: 30px;
}

.add_vip_title2_font1 {
  flex: 0.2;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  margin: 0 0 10px 30px;
}

.add_vip_title2_font2 {
  flex: 0.6;
  height: 30px;
}

.add_vip_title2_font2_input {
  border: 1px solid #e5e5e5;
  outline: none;
  height: 40px;
  box-sizing: border-box;
  padding-left: 10px;
}

.add_vip_title2_font3 {
  line-height: 40px;
  background: #3e63dd;
  color: white;
  font-size: 14px;
  width: 60px;
  height: 40px;
  text-align: center;
}

.add_vip_title3 {
  width: 100%;
  height: 50px;
  line-height: 50px;
  /*background: #E5E5E5;*/
  font-size: 12px;
  font-weight: 400;
  /*border-bottom: 1px solid #E5E5E5;*/
  /*border-top: 1px solid #E5E5E5;*/
}

.add_vip_title3_font1 {
  margin-left: 30px;
}

.add_vip_title4 {
  margin: 15px 30px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  max-height: 140px;
  overflow: hidden;
  overflow-y: auto;
}

.add_vip_title4::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.add_vip_title4::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.add_vip_title4::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.add_vip_title4_font0 {
  padding: 8px 12px;
  color: #ccc;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin: 0 10px 10px 0;
}

.tag_active {
  padding: 8px 12px;
  color: #3e63dd;
  border: 1px solid #3e63dd;
  border-radius: 4px;
  margin: 2px;
}

.hy_model1_Editing_dangan {
}

.add_dangan_info1 {
  display: flex;
  padding: 10px 30px;
  border-bottom: 1px solid #e5e5e5;
}

.add_dangan_info1_font1 {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  height: 30px;
  line-height: 30px;
}

.add_dangan_info1_font2 {
  border: 0;
  background: 0;
  outline: none;
  width: 100%;
  font-size: 12px;
  color: #999999;
  line-height: 30px;
  margin-left: 20px;
}

/*改变浏览器窗口大小的时候，表头和表身不对齐*/
.el-table th {
  display: table-cell !important;
}

/*余额明细*/
.memberBalanceStyle .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: calc(100vh - 210px);
  overflow-y: auto;
}

.memberBalanceStyle .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.memberBalanceStyle .el-table__body-wrapper::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.memberBalanceStyle .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/*权益明细列表*/
.memberBenfitStyle .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: calc(100vh - 258px);
  overflow-y: auto;
}

.memberBenfitStyle .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.memberBenfitStyle .el-table__body-wrapper::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.memberBenfitStyle .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.memberBalanceFengye {
  padding: 10px 5px;
}

.memberBalanceRemark {
  overflow: hidden;
  text-overflow: ellipsis;
}
/*余额明细结束*/

.memberboxStyle .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: 400px;
  overflow-y: auto;
}
.memberboxStyle .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.memberboxStyle .el-table__body-wrapper::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.memberboxStyle .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/* 覆盖取消按钮默认样式 */
.el-button--default:hover {
  color: #3e63dd;
  border-color: #dcdfe6 !important;
  background-color: #fff !important;
}

.entityComfirmBtn:hover {
  background: #3e63dd !important;
  border-color: #3e63dd !important;
  color: #fff !important;
}

.f_vip_detail_title {
  display: flex;
}

.f_vip_detail_title > div {
  padding: 5px 0;
}

.f_vip_detail_title > div:nth-child(1) {
  flex-shrink: 0;
  width: 76px;
  color: #9ca3af;
}
