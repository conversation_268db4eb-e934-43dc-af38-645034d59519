<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>预约</title>
    <link rel="stylesheet" href="./css/css-comment.css" />
    <link rel="stylesheet" href="./vue/element/<EMAIL>" />
    <link rel="stylesheet" href="./css/shop_index.css" />
    <link rel="stylesheet" href="./component/css/component.css" />
  </head>
  <body>
    <div id="reservation" v-cloak>
      <div class="reservation-wrap">
        <div class="navigation-tab">
          <ul class="tab-ul">
            <li v-for="(item,index) in navigationArr">
              <p
                class="tab-navigation"
                :class="tabIndex==index?'tab-liActive':''"
                @click.stop="bindTab(index)"
              >
                {{item.navigation}}
              </p>
              <ol v-show="item.findArr && item.isTab">
                <li
                  class="tab-navigation2"
                  v-for="(find,i) in item.findArr"
                  :class="findIndex==i?'tabText':''"
                  @click="bindFindTab(i)"
                >
                  {{find.navigation}}
                </li>
              </ol>
            </li>
          </ul>
        </div>
        <div
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0)"
          class="reservation-container"
        >
          <!--预约上门 and 预约到店-->
          <div v-show="tabIndex==0" class="form_data">
            <!-- <el-card v-if='findIndex==0 && bookerdoor==2' class="add_reservation_card">
                    不支持预约上门
                </el-card> -->
            <el-card class="add_reservation_card">
              <div class="form-wrap">
                <div class="form_block">
                  <label class="form-label">手机号</label>
                  <div class="form-inner">
                    <!-- <input class="form-input" type="text"
                                    v-model.trim="formReservation.phone" maxlength="11"
                                    @input="bindInquireMember(formReservation.phone)"
                                    @keyup.enter.exact.stop="bindInquire(formReservation.phone)"
                                    placeholder="请输入或刷实体卡"> -->
                    <el-input
                      v-model.trim="formReservation.phone"
                      placeholder="请输入或刷实体卡"
                      maxlength="11"
                      @input="bindInquireMember(formReservation.phone)"
                      @keyup.enter.exact.stop="bindInquire(formReservation.phone)"
                      class="form-input"
                    ></el-input>
                  </div>
                </div>
                <div class="form_block" style="border-bottom: none">
                  <label v-show="findIndex==0" class="form-label">预约人</label>
                  <!-- <label v-show="findIndex==1" class="form-label">到店人</label> -->
                  <label v-show="findIndex==1" class="form-label">预约人</label>
                  <div class="form-inner">
                    <!-- <input class="form-input" type="text"
                                    v-model.trim="formReservation.people" type="text" placeholder="请输入"> -->
                    <el-input
                      class="form-input"
                      v-model.trim="formReservation.people"
                      placeholder="请输入"
                    ></el-input>
                  </div>
                </div>
                <div class="form_block">
                  <label class="form-label" v-if="findIndex==1">到店时间</label>
                  <label class="form-label" v-if="findIndex==0">上门时间</label>
                  <div class="form-inner">
                    <!--<el-date-picker-->
                    <!--v-model="formReservation.time" @change="changeTime"-->
                    <!--type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"-->
                    <!--placeholder="选择日期时间">-->
                    <!--</el-date-picker>-->
                    <span
                      style="color: #3e63dd; font-size: 15px; cursor: pointer"
                      @click="chooseTime"
                      v-if="formReservationTime"
                    >
                      {{formReservationTime}}
                    </span>
                    <el-button @click="chooseTime" v-else>选择时间</el-button>
                    <!--<el-date-picker v-model="formReservation.time" type="datetime" placeholder="选择日期"-->
                    <!--format="yyyy-MM-dd"-->
                    <!--value-format="yyyy-MM-dd"></el-date-picker>-->
                  </div>
                </div>
              </div>
              <div class="form-wrap2">
                <div class="form_block2">
                  <label class="form-label2">服务预约</label>
                  <div class="form-inner2">
                    <div class="axb">
                      <div
                        class="add-server"
                        v-for="(item,index) in selectService"
                      >
                        <i
                          class="el-icon-remove"
                          @click.stop="bindRem(index)"
                        ></i>
                        <div class="server-list">
                          <div class="form-inner2">
                            <p>
                              <span
                                class="server-text"
                                @click="addServer(index)"
                              >
                                {{item.server}}
                              </span>
                              <span class="server-text" v-if="item.skuName">
                                | {{item.skuName}}
                              </span>
                            </p>
                            <span>
                              <span v-if="item.serverPrice">
                                ￥ {{item.serverPrice}}
                              </span>
                              <!-- <i class="el-icon-arrow-right"></i> -->
                            </span>
                          </div>
                          <div class="form-inner2">
                            <span
                              class="server-text"
                              @click="bindTechnician(index)"
                            >
                              选择
                              <technician-name></technician-name>
                            </span>
                            <!-- <i class="el-icon-arrow-right"></i> -->
                          </div>
                        </div>
                      </div>
                    </div>
                    <p class="addTo" @click="bindAddTo">
                      添加服务和
                      <technician-name></technician-name>
                    </p>
                  </div>
                </div>
              </div>
              <div class="form-wrap" style="margin-bottom: 25px">
                <div class="form_block" v-if="findIndex==0">
                  <label class="form-label">上门地址</label>
                  <div class="form-inner">
                    <!-- <input class="form-input" type="text" v-model="formReservation.address" type="text"
                                    placeholder="请输入"> -->
                    <el-input
                      class="form-input"
                      v-model="formReservation.address"
                      placeholder="请输入"
                    ></el-input>
                  </div>
                </div>
                <div class="form_block">
                  <label class="form-label">会员备注</label>
                  <div class="form-inner">
                    <!-- <input class="form-input" type="text" v-model="formReservation.remarks" placeholder="请输入"> -->
                    <el-input
                      class="form-input"
                      v-model="formReservation.remarks"
                      placeholder="请输入"
                    ></el-input>
                  </div>
                </div>
              </div>
              <div class="reservation-btn">
                <span style="margin-right: 15px">
                  待支付：￥{{reservationMoney}}
                </span>
                <el-button type="primary" @click="bindSubReservation">
                  提交
                </el-button>
                <!--<el-button type="primary" @click="reservationEmpty">清空</el-button>-->
              </div>
            </el-card>
          </div>
          <!-- 查看预约 -->
          <div v-show="tabIndex==1" class="see-reservation">
            <div class="shop-tab" style="border-bottom: 0">
              <el-radio-group
                v-model="tabCur"
                size="medium"
                @change="tabCurReservation"
              >
                <el-radio-button label="6">全部</el-radio-button>
                <el-radio-button label="1">待服务</el-radio-button>
                <el-radio-button label="2">已超时</el-radio-button>
                <el-radio-button label="3">已开单</el-radio-button>
                <el-radio-button label="4">已取消</el-radio-button>
                <!--<el-radio-button label="5">未分配</el-radio-button>-->
              </el-radio-group>
            </div>

            <div class="reservation-search">
              <div style="margin-bottom: 15px">
                <div class="el-input el-input--suffix">
                  <input
                    type="text"
                    autocomplete="off"
                    @keyup.enter.exact.stop="bingGetSearch"
                    placeholder="请输入会员编号或手机号"
                    v-model.trim="reservationCall"
                    class="el-input__inner"
                  />
                  <span class="el-input__suffix" @click="getallBooker">
                    <span class="el-input__suffix-inner">
                      <i class="el-input__icon el-icon-search"></i>
                    </span>
                  </span>
                </div>
              </div>
              <div>
                <el-table
                  :data="tableData"
                  class="reservation-table"
                  ref="reservationTable"
                  @row-click="bindSeeDetails"
                  stripe
                >
                  <el-table-column type="expand">
                    <template
                      slot-scope="props"
                      v-if="props.row.servermess !== undefined && props.row.servermess.length >0"
                    >
                      <el-form
                        label-position="left"
                        inline
                        class="reservation_table_expand"
                      >
                        <el-form-item
                          label="服务"
                          v-for="(item,index) in props.row.servermess"
                          :key="item.id"
                        >
                          <span>{{item.name}}</span>
                        </el-form-item>
                      </el-form>
                    </template>
                  </el-table-column>
                  <el-table-column label="下单时间" prop="addtime">
                    <template slot-scope="scope">
                      <p v-if="scope.row.addtime">{{scope.row.addtime}}</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="到店人">
                    <template slot-scope="scope">
                      <el-tooltip
                        v-if="scope.row.shoper"
                        class="item"
                        effect="dark"
                        :content="scope.row.shoper"
                        placement="top-start"
                      >
                        <p class="shoperName">{{scope.row.shoper}}</p>
                      </el-tooltip>
                      <p class="table_date" v-if="scope.row.phone">
                        {{scope.row.phone}}
                      </p>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="服务">
                                <template slot-scope="scope"
                                          v-if="scope.row.servermess !== undefined && scope.row.servermess.length >0">
                                    <ul>
                                        <li v-for="(item,index) in scope.row.servermess">
                                            <span>{{item.name}}</span>
                                        </li>
                                    </ul>
                                </template>
                            </el-table-column> -->
                  <el-table-column label="">
                    <template slot="header" slot-scope="scope">
                      <technician-name></technician-name>
                    </template>
                    <template
                      slot-scope="scope"
                      v-if="scope.row.servermess !== undefined && scope.row.servermess.length >0"
                    >
                      <ul>
                        <li
                          v-for="(item,index) in scope.row.servermess"
                          :key="item.id"
                        >
                          <span>{{item.nickname || "无"}}</span>
                        </li>
                      </ul>
                    </template>
                  </el-table-column>
                  <el-table-column prop="state" label="类型">
                    <template slot-scope="scope">
                      <span style="color: #3e63dd" v-show="scope.row.state==2">
                        预约上门
                      </span>
                      <span style="color: #3e63dd" v-show="scope.row.state==1">
                        预约到店
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="arrdate" label="时间">
                    <template slot-scope="scope">
                      <p v-if="scope.row.arrdate">{{scope.row.arrdate}}</p>
                      <p
                        v-if="scope.row.status!='已开单' && scope.row.status!='已取消' && scope.row.moretime"
                        style="color: #999; font-size: 14px"
                      >
                        已超时{{scope.row.moretime}}
                      </p>
                      <p
                        v-if="scope.row.status=='已开单' && scope.row.billtime!=null"
                        style="color: #999; font-size: 14px"
                      >
                        {{scope.row.billtime}}开单
                      </p>
                      <p
                        v-if="scope.row.status=='已取消' && scope.row.canceltime!=null"
                        style="color: #999; font-size: 14px"
                      >
                        {{scope.row.canceltime}}取消
                      </p>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="block" v-if="tableData.length>0 || allCount>0">
                <el-pagination
                  background
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :page-size="limit"
                  :current-page="currentPage"
                  layout="total, prev, pager, next, jumper"
                  :total="allCount"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-dialog
        title="预约详情"
        :visible.sync="isDetails"
        top="45px"
        :close-on-click-modal="false"
        :close-on-press-escape="true"
        custom-class="notice-mask"
        width="700px"
        :show-close="false"
      >
        <div slot="title">
          <div class="dialog-title">
            <i
              class="el-dialog__close el-icon el-icon-close"
              @click.stop="bindCloseisDetails"
            ></i>
            <span style="flex: 1; text-align: center">预约详情</span>
            <!--支付状态 1.待支付 2.已支付 -->
            <p class="cancelReservation">
              <span
                v-if="tabCur==1 || tabCur==2"
                @click="isCancelReservation = true"
              >
                取消预约
              </span>
            </p>
          </div>
        </div>
        <div>
          <ul class="detailsTable-ul">
            <li class="detailsTable-li">
              <p class="margin-bottom shoperName" :title="detailsTable.shoper">
                {{detailsTable.shoper}}
              </p>
              <p class="margin-bottom">
                <span>{{detailsTable.phone}}</span>
                <span class="shoper-tip">预约人</span>
              </p>
            </li>
            <li class="detailsTable-li">
              <p
                class="margin-bottom shoperName"
                :title="detailsTable.place_shoper"
              >
                {{detailsTable.place_shoper}}
              </p>
              <p class="margin-bottom">
                <span>{{detailsTable.place_phone}}</span>
                <span class="shoper-tip">下单人</span>
              </p>
              <p>会员编号:{{detailsTable.member_number}}</p>
            </li>
            <li class="detailsTable-li" style="text-align: right">
              <p v-if="(tabCur==3 || tabCur==6) && detailsTable.billtime">
                已开单
              </p>
              <p v-if="(tabCur==4  || tabCur==6) && detailsTable.canceltime">
                已取消
              </p>
              <p
                v-if="(tabCur==2 || tabCur==6) && detailsTable.moretime!='' && detailsTable.canceltime==null"
              >
                已超时
              </p>
              <p
                v-if="tabCur==1 || (tabCur==6 && detailsTable.moretime =='' && detailsTable.billtime == null && detailsTable.canceltime ==null)"
              >
                待服务
              </p>
              <p
                v-if="tabCur==3 || tabCur==4 || (tabCur==6 && (detailsTable.billtime || detailsTable.canceltime))"
                class="margin-bottom"
              >
                {{detailsTable.arrdate}}
              </p>
              <el-date-picker
                v-if="tabCur==1 || tabCur==2 || (tabCur==6 && detailsTable.billtime == null && detailsTable.canceltime ==null)"
                class="arrdate"
                style="margin-bottom: 5px"
                v-model="detailsTable.arrdate"
                type="datetime"
                size="small"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                prefix-icon="el"
                @change="modifyAppointment"
              ></el-date-picker>
              <!-- <p v-if="tabCur==1 || tabCur==2"
                        class="margin-bottom"
                        style="color: #3E63DD;font-size: 15px;cursor: pointer;" @click="chooseTime" >{{detailsTable.arrdate}}</p>
                    <el-button @click="chooseTime" v-else>选择时间</el-button> -->

              <ul>
                <li v-if="(tabCur==3 || tabCur==6) && detailsTable.billtime">
                  {{detailsTable.billtime}}开单
                </li>
                <li v-if="(tabCur==4 || tabCur==6) && detailsTable.canceltime">
                  {{detailsTable.canceltime}}取消
                </li>
                <li
                  v-if="(tabCur==2 || tabCur==6) && detailsTable.canceltime==null && detailsTable.moretime!=''"
                >
                  已超时{{detailsTable.moretime}}
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div class="details-server">
          <div class="details-wrap">
            <label class="details-label">服务</label>
            <div class="details-server-wrap">
              <div
                class="details-server-info"
                v-for="(item,index) in detailsTable.servermess"
                :key="item.id"
              >
                <div class="details-product">
                  <div>
                    <p class="details-product-list">
                      <span>{{item.name}}</span>
                      <span v-if="item.price">￥{{item.price}}*1</span>
                      <span v-if="item.sertime">(约{{item.sertime}}分钟)</span>
                    </p>
                  </div>
                </div>
                <div class="details-technician">
                  <p>
                    <span>
                      <technician-name></technician-name>
                      ：
                    </span>
                    <span
                      v-if="item.nickname"
                      class="changeActive"
                      @click="bindChangeTechnician"
                      style="color: #333"
                    >
                      {{item.nickname}}
                    </span>
                    <span
                      v-if="!item.nickname"
                      @click="bindChangeTechnician"
                      style="cursor: pointer"
                    >
                      无
                    </span>
                  </p>
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
              <p class="details-order-status">
                <span class="seeDetails">{{detailsTable.paytype}}</span>
              </p>
            </div>
          </div>
          <div class="details-wrap" v-if="detailsTable.address">
            <label class="details-label">地址</label>
            <p class="details-server-info">{{detailsTable.address}}</p>
          </div>
          <div class="details-wrap">
            <label class="details-label">备注</label>
            <p class="details-server-info" v-if="detailsTable.remarks">
              {{detailsTable.remarks}}
            </p>
            <p class="details-server-info" v-else>无</p>
          </div>
          <div class="details-wrap" v-if="tabCur==4">
            <label class="details-label" style="width: 110px">取消原因</label>
            <p class="details-server-info" v-if="detailsTable.reason">
              {{detailsTable.reason}}
            </p>
            <p class="details-server-info" v-else>暂无原因</p>
          </div>
        </div>
        <div class="dialog-footer">
          <div>
            <p class="reservation-price">合计 ￥{{detailsTable.payment}}</p>
            <p
              class="reservation-all"
              v-if="detailsTable.servermess !== undefined && detailsTable.servermess.length >0"
            >
              <span style="display: block; margin-bottom: 5px">
                共{{detailsTable.servermess.length}}项
              </span>
            </p>
          </div>
          <div class="reservation-group" v-if="detailsTable.status == 1">
            <p
              class="save-btn reservationBtn"
              style="color: #333"
              @click="bindSave"
            >
              保存修改
            </p>
            <p class="billing-btn reservationBtn" @click="serviceBilling">
              服务开单
            </p>
          </div>
        </div>
      </el-dialog>

      <el-dialog
        title="取消预约"
        :visible.sync="isCancelReservation"
        class="cancelReservation-mask"
        width="400px"
      >
        <el-select
          v-model="reasonIndex"
          style="width: 100%"
          placeholder="请选择"
          @change="bindChooseReason"
        >
          <el-option
            v-for="item in cancelReason"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>

        <div slot="footer" class="dialog-footer">
          <el-button @click="isCancelReservation = false">取 消</el-button>
          <el-button type="primary" @click="bindConfirmReservation">
            确 定
          </el-button>
        </div>
      </el-dialog>

      <!--到店预约收款-- 上门预约收款 -->
      <!--<el-dialog title="预约详情" :visible.sync="isDetails" :close-on-click-modal="false"-->
      <!--:close-on-press-escape="true" custom-class="details-mask" top="0" width="40%" :show-close="false">-->
      <!--<div slot="title">-->
      <!--<div class="dialog-title">-->
      <!--<i class="el-dialog__close el-icon el-icon-close" @click.stop="bindCloseisDetails"></i>-->
      <!--<span>预约详情</span>-->
      <!--<span class="cancelReservation">取消预约</span>-->
      <!--</div>-->
      <!--</div>-->
      <!--</el-dialog>-->

      <el-dialog
        class="add-server-mask"
        title="添加服务"
        :visible.sync="isServer"
        width="600px"
      >
        <add-server
          :member-info="memberInfo"
          :server-list="serverListArr"
          @server-determine="confirmServer"
        ></add-server>
      </el-dialog>

      <el-dialog
        class="add-server-mask"
        :visible.sync="isTechnician"
        width="600px"
      >
        <template slot="title">
          <technician-name></technician-name>
          代收
        </template>
        <!-- <add-server :server-list="technicianArr" @server-determine="confirmTechnician"></add-server> -->
        <div>
          <div class="tianjia_fuwu_mian">
            <div class="fuwu_biaoti">
              <ul v-if="technicianArr" v-for="(item,index) in technicianArr">
                <li
                  class="tianjia_fuwu_font"
                  :class="curIndex == index ? 'serverActive':'' "
                  @click="bindServerTab(index,item)"
                >
                  <span v-if="item.label_name">{{item.label_name}}</span>
                  <span v-if="item.group_name">{{item.group_name}}</span>
                </li>
              </ul>
            </div>
            <div
              class="fuwu_biaoti_chioce"
              v-if="technicianArr.length>0 && technicianArr[curIndex].serMess"
            >
              <ul class="fuwu_biaoti_chioce_bottom" style="border-bottom: 0px">
                <li
                  v-for="(item,index) in technicianArr[curIndex].serMess"
                  class="server_biaoti_name_font"
                  style="
                    padding: 6px 20px;
                    border-bottom: 0.5px solid rgba(221, 221, 221, 1);
                  "
                >
                  <!-- <div v-if="item.service_name" class="server_biaoti_name_font1">
                                <img :src="item.img_arr[0]" :title="item.service_name" onerror="this.src='./images/default.jpg'">
                            </div> -->
                  <div
                    style="line-height: 56px"
                    class="server_biaoti_name_font1"
                  >
                    <div class="xuanze_jishi_name_check">
                      <el-checkbox
                        v-model="item.checked"
                        style="font-size: 16px"
                        @change="bindSelectService(item,technicianArr[curIndex].serMess)"
                      >
                        {{item.nickname}}
                      </el-checkbox>
                    </div>
                  </div>
                  <div>
                    <!-- <p ><span ></span></p> -->
                    <div
                      class="server_biaoti_name_font2"
                      style="display: flex; width: 80px"
                    >
                      <div
                        class="xuanze_jishi_server_font"
                        style="margin-right: 6px"
                      >
                        点客
                      </div>
                      <el-switch
                        v-model="item.isGuest"
                        active-color="#3e63dd"
                        @change="verifyGuest(item)"
                        inactive-color="#999999"
                      ></el-switch>
                    </div>
                  </div>
                </li>
                <li
                  v-if="technicianArr.length>0 && technicianArr[curIndex].serMess && technicianArr[curIndex].serMess.length==0"
                  style="padding-left: 10px"
                >
                  没有可选择的
                  <technician-name></technician-name>
                </li>
              </ul>
            </div>
          </div>
          <div
            slot="footer"
            class="server-footer"
            style="padding: 20px 10px 0 0"
          >
            <el-button class="cancel_server" @click="cancelChooseTechno">
              取消
            </el-button>
            <el-button type="primary" @click="saveChooseTechno">确定</el-button>
          </div>
        </div>
      </el-dialog>

      <el-dialog
        class="add-server-sku"
        title="选择规格"
        :visible.sync="isSku"
        :show-close="false"
        width="600px"
      >
        <div slot="title" class="srever_sku-title">
          <div class="srever_sku">选择规格</div>
          <i class="el-icon-back" @click="isSku = false"></i>
        </div>
        <div class="sku-body">
          <p style="margin-bottom: 10px">
            <span>服务>{{skuTitle}}</span>
            <span v-show="skuCategory">>{{skuCategory}}</span>
          </p>
          <ul class="sku-list">
            <li
              class="sku-item"
              v-for="(item,index) in skuArr"
              @click="bindSku(item)"
            >
              <span class="itemSku" style="margin-right: 15px">
                {{item.sku}}
              </span>
              <template v-if="memberInfo && memberInfo.id">
                <span class="itemPrice">￥{{item.price}}</span>
                <!-- <span>会员价：￥{{item.minPrice}}</span> -->
              </template>
              <template v-else>
                <span class="itemPrice">￥{{item.price}}</span>
              </template>
            </li>
          </ul>
        </div>
      </el-dialog>

      <el-dialog
        class="add-choose-time"
        title="选择预约时间"
        :visible.sync="isChooseTime"
        top="7vh"
        width="800px"
      >
        <div
          class="reservation-choose-time"
          style="width: 100% !important; height: auto !important"
        >
          <ul class="reservation-choose-time-ul">
            <li
              v-for="item of dayLabel"
              :class="item.isactive?chooseTimeU:chooseTimeUli"
            >
              <div @click="chooseTimeDay(item)">
                <p class="choose-time-label">{{item.label}}</p>
                <p class="open_order_search">{{item.time}}</p>
              </div>
            </li>
          </ul>
          <div class="choose-time-body">
            <div
              class="choose-time-item"
              v-for="item of timeLabel"
              @click="chooseTimeLabel(item)"
            >
              <span v-if="item.isactive==1" class="time-item-normal">
                {{item.time}}
              </span>
              <span v-if="item.isactive==2" class="time-item-active">
                {{item.time}}
              </span>
              <span v-if="item.isactive==3" class="time-item-expire">
                {{item.time}}
              </span>
              <span v-if="item.isactive==4" class="time-item-select">
                {{item.time}}
              </span>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </body>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <script src="js/unocss.theme.js"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <script src="./component/components.js"></script>
  <script src="js/reservation.js"></script>
</html>
