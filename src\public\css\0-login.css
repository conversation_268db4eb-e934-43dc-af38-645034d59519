.login-Wrap {
  background: #fff;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  position: relative;
}

.drag {
  -webkit-app-region: drag;
}

.no-drag {
  -webkit-app-region: no-drag;
}

.close {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 24px;
  cursor: pointer;
}

.bg_login {
  width: 100%;
  height: 100vh;
  /* background: url('../images/bg_login.png') no-repeat; */
  background: #fff;
  /* background-size: cover; */
  position: relative;
  top: 0px;
  left: 0px;
}

.input_login {
  width: 400px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  padding: 40px;
  border-radius: 8px;
}

.login_choice {
  margin-bottom: 70px;
  font-size: 24px;
  font-weight: 600;
}

.form-wrap {
  box-sizing: border-box;
}

.password_font {
  font-size: 20px;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
  /*border-right: 2px solid #E5E5E5;*/
  box-sizing: border-box;
  padding-right: 15px;
}

.password_font:hover {
  cursor: pointer;
}

.code_font:hover {
  cursor: pointer;
}

.code_font {
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  box-sizing: border-box;
  padding-left: 10px;
}

.telephone {
  width: 100%;
  height: 45px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(229, 229, 229, 1);
}

.telephone span {
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 45px;
}

.password {
  width: 100%;
  height: 45px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(229, 229, 229, 1);
  margin-top: 30px;
}

.password span {
  font-size: 24px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 45px;
}

.password img {
  width: 37px;
  height: 28px;
  display: inline-block;
}

.password img:hover {
  cursor: pointer;
}

.login {
  width: 100%;
  box-sizing: border-box;
  padding: 15px 0;
  font-size: 16px;
}

.login-label {
  display: inline-block;
  width: 70px;
  height: 48px;
  line-height: 48px;
  font-size: 16px;
  color: #333;
  text-align: left;
  box-sizing: border-box;
  padding-left: 10px;
  cursor: pointer;
}

.el-input--prefix .el-input__inner {
  padding-left: 70px;
}

.el-input__inner {
  height: 48px;
  line-height: 48px;
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  color: #fff;
  background-color: #3e63dd;
  border-color: #3e63dd;
}

.version {
  position: absolute;
  bottom: 10px;
  font-size: 14px;
}

.version-title {
  font-size: 16px;
  margin-bottom: 5px;
}

.version-ul {
  box-sizing: border-box;
  padding-left: 15px;
  font-size: 16px;
}
.version-ul li {
  margin-bottom: 5px;
  line-height: 20px;
  font-family: "Microsoft YaHei", "SimHei", "SimSun";
}

.logo_set {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 45px;
}
.logo-img {
  max-width: 61px;
  max-height: 60px;
  display: block;
}
.logo-text {
  color: #3e63dd;
  font-size: 24px;
  padding-left: 20px;
}
