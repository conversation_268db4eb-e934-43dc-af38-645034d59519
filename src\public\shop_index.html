<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>商品-详情</title>
    <link rel="stylesheet" href="./css/css-comment.css" />
    <link rel="stylesheet" href="./vue/element/<EMAIL>" />
    <link rel="stylesheet" href="./css/shop_index.css" />
    <link rel="stylesheet" href="./component/css/component.css" />
    <link rel="stylesheet" href="./css/card.css" />
  </head>
  <body>
    <div id="shop" v-cloak>
      <el-row class="shop-comtent" v-cloak>
        <el-col
          :sm="10"
          :md="10"
          :lg="8"
          :xl="8"
          class="height"
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0)"
        >
          <div class="shop_left">
            <div class="shop-tab">
              <el-radio-group v-model="tabCur" size="medium" @change="bindTab">
                <el-radio-button label="0">服务</el-radio-button>
                <el-radio-button label="1">卡项</el-radio-button>
                <el-radio-button label="2">产品</el-radio-button>
              </el-radio-group>
            </div>
            <div>
              <div class="shop_public">
                <div class="shop-body-search">
                  <!--<el-input placeholder="请输入服务名称" suffix-icon="el-icon-search"></el-input>-->

                  <div class="el-input el-input--suffix">
                    <input
                      type="text"
                      autocomplete="off"
                      :placeholder="placeholderText"
                      v-model.trim="keyword"
                      ref="goodsKeyWord"
                      class="el-input__inner"
                      @keyup.enter.exact.stop="bindSearch"
                    />
                    <span class="el-input__suffix" @click="bindSearch">
                      <span class="el-input__suffix-inner">
                        <i class="el-input__icon el-icon-search"></i>
                      </span>
                    </span>
                  </div>
                </div>
                <ul class="shop-body-tab">
                  <el-popover
                    placement="bottom"
                    width="300"
                    trigger="click"
                    v-model="filterTabel.isType"
                  >
                    <div class="popover-wrap">
                      <div>
                        <p class="popover-title">标签筛选</p>
                        <span
                          class="cancel-popover"
                          @click.stop="bindIsTypeCancel(0)"
                        >
                          取消
                        </span>
                      </div>
                      <ul class="popover-ul">
                        <li class="popover-li" @click.stop="bindLabel(0,-1,0)">
                          <span>所有标签</span>
                          <i
                            v-if="filterTabel.typeIndex==-1"
                            class="el-icon-circle-check"
                          ></i>
                        </li>
                        <li
                          class="popover-li"
                          v-for="(item,index) in labelArr"
                          @click.stop="bindLabel(0,index,item)"
                        >
                          <span v-if="item.classification_name">
                            {{item.classification_name}}
                          </span>
                          <span v-if="item.label_name">
                            {{item.label_name}}
                          </span>
                          <span v-if="item.name">{{item.name}}</span>
                          <i
                            v-if="filterTabel.typeIndex==index"
                            class="el-icon-circle-check"
                          ></i>
                        </li>
                      </ul>
                    </div>
                    <li class="body-tab-li" slot="reference">
                      <span>标签</span>
                      <i class="el-icon-caret-bottom"></i>
                    </li>
                  </el-popover>
                  <el-popover
                    placement="bottom"
                    width="250"
                    trigger="click"
                    v-model="filterTabel.isStatus"
                  >
                    <div class="popover-wrap">
                      <div>
                        <p class="popover-title">状态筛选</p>
                        <span
                          class="cancel-popover"
                          @click.stop="bindIsTypeCancel(1)"
                        >
                          取消
                        </span>
                      </div>
                      <ul class="popover-ul">
                        <li
                          class="popover-li"
                          v-for="(item,index) in labelStatusArr"
                          @click.stop="bindLabel(1,index,item)"
                        >
                          <span>{{item.label}}</span>
                          <i
                            v-if="filterTabel.statusIndex==index"
                            class="el-icon-circle-check"
                          ></i>
                        </li>
                      </ul>
                    </div>
                    <li class="body-tab-li" slot="reference">
                      <span>状态</span>
                      <i class="el-icon-caret-bottom"></i>
                    </li>
                  </el-popover>
                  <el-popover
                    placement="bottom"
                    width="280"
                    trigger="click"
                    getProductData
                    v-model="filterTabel.isSort"
                  >
                    <div class="popover-wrap">
                      <div>
                        <p class="popover-title">排序筛选</p>
                        <span
                          class="cancel-popover"
                          @click.stop="bindIsTypeCancel(2)"
                        >
                          取消
                        </span>
                      </div>
                      <ul class="popover-ul">
                        <li
                          class="popover-li"
                          v-for="(item,index) in labelSortArr"
                          @click.stop="bindLabel(2,index,item)"
                        >
                          <span>{{item.label}}</span>
                          <i
                            v-if="filterTabel.sortIndex==index"
                            class="el-icon-circle-check"
                          ></i>
                        </li>
                      </ul>
                    </div>
                    <li class="body-tab-li" slot="reference">
                      <span>排序</span>
                      <i class="el-icon-caret-bottom"></i>
                    </li>
                  </el-popover>
                </ul>
              </div>

              <!--服务-->
              <div v-show="tabCur == 0" class="shop-body" ref="goodsServer">
                <!--class="server-shop-box"-->
                <div
                  v-infinite-scroll="loadMore"
                  infinite-scroll-disabled="isScroll"
                  infinite-scroll-distance="10"
                  infinite-scroll-immediate-check="isScroll"
                >
                  <ul>
                    <li
                    v-for="(item,index) in shopArr"
                      :class="serverIndex==index?'shopActive':''"
                      class="server-shop"
                    @click="bindShop(0,index,item)"
                  >
                      <!--<p>{{item.name}}</p>-->
                      <div class="shopImg">
                      <img
                        :src="item.imgurl"
                        onerror='this.src="images/default.jpg"'
                        :alt="item.service_name"
                      />
                    </div>
                      <div class="shop-list">
                        <el-tooltip
                          class="item"
                          effect="light"
                          :disabled="!isTextOverflow(item.service_name, 14)"
                          :content="item.service_name"
                          placement="top"
                        >
                          <p class="shop_name">{{item.service_name}}</p>
                        </el-tooltip>
                        <p class="clear-float fzc9">
                          <span>{{item.label_name}}</span>
                          <span class="server-price">￥{{item.price}}</span>
                        </p>
                        <p class="clear-float fzc9">
                          <span>销售 {{item.sellnum}}</span>
                          <el-tag
                            class="server-statu"
                            size="medium"
                            :type="item.status==1?'':'danger'"
                          >
                            {{item.status==1?'上架中':'未上架'}}
                          </el-tag>
                        </p>
                      </div>
                    </li>
                  </ul>

                  <!-- 加载效果 -->
                  <div v-if="busy" class="loadingtip">{{loadingtip}}</div>
                </div>
              </div>

              <!--卡项-->
              <div
                v-show="tabCur == 1"
                class="shop-body"
                style="height: calc(100vh - 210px)"
                ref="goodsCard"
                style="padding: 0"
              >
                <div
                  v-infinite-scroll="loadMoreCard"
                  infinite-scroll-disabled="isCardScroll"
                  infinite-scroll-distance="10"
                  infinite-scroll-immediate-check="isCardScroll"
                >
                  <div
                    v-for="(item,index) in cardArr"
                    :class="cardIndex==index?'cardActive':''"
                    class="card-shop"
                    @click="bindShop(1,index,item)"
                  >
                    <div class="cardBg">
                      <div class="card-info">
                        <el-tooltip
                          class="item"
                          effect="light"
                          :disabled="!isTextOverflow(item.card_name, 19)"
                          :content="item.card_name"
                          placement="top"
                        >
                          <p class="card-name">{{item.card_name}}</p>
                        </el-tooltip>
                        <p class="opacity-80">
                          <span v-if="item.permanent==2">
                            有效时间：{{item.validity_time}}天
                          </span>
                          <span v-if="item.permanent==1">永久有效</span>
                        </p>
                        <p class="giveAway-faceValue">
                          <span
                            class="giveAway"
                            :class="cardIndex!=index?'giveAwayActive':''"
                            v-if="item.give && item.give>0"
                          >
                            赠送￥{{item.give | filterMoney}}
                          </span>
                          <span v-else></span>
                          <span class="faceValue">
                            ￥
                            <span>{{item.realPrice}}</span>
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 加载效果 -->
                  <div v-if="busyCard" class="loadingtip">{{loadingtip}}</div>
                </div>
              </div>

              <!--产品-->
              <div v-show="tabCur == 2" class="shop-body" ref="goodsProduct">
                <div
                  v-infinite-scroll="loadMoreProduct"
                  infinite-scroll-disabled="isProductScroll"
                  infinite-scroll-distance="10"
                  infinite-scroll-immediate-check="isProductScroll"
                >
                  <ul class="server-shop-box">
                    <li
                      v-for="(item,index) in productArr"
                      :class="productIndex==index?'shopActive':''"
                      class="server-shop"
                      @click="bindShop(2,index,item)"
                    >
                      <div class="shopImg">
                        <img
                          :src="item.imgarr[0]"
                          onerror='this.src="images/default.jpg"'
                          :alt="item.product_name"
                        />
                      </div>
                      <div class="shop-list">
                        <el-tooltip
                          class="item"
                          effect="light"
                          :disabled="!isTextOverflow(item.product_name, 14)"
                          :content="item.product_name"
                          placement="top"
                        >
                          <p class="shop_name">{{item.product_name}}</p>
                        </el-tooltip>
                        <div class="flex justify-between items-center">
                          <div>{{item.s_label}}</div>
                          <div>{{item.s_price}}</div>
                        </div>
                        <div class="flex justify-between items-center">
                          <div>已售 {{item.totalsell}}</div>
                          <el-tag
                            class="server-statu"
                            size="medium"
                            :type="item.status=='上架中'?'':'danger'"
                          >
                            {{item.status}}
                          </el-tag>
                        </div>
                      </div>
                    </li>
                  </ul>

                  <!-- 加载效果 -->
                  <div v-if="busyProduct" class="loadingtip">
                    {{loadingtip}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <el-col
          :sm="14"
          :md="14"
          :lg="16"
          :xl="16"
          class="height"
          v-loading="detailsLoading"
        >
          <!--服务上下架-->
          <div v-show="tabCur == 0" class="shop_right height">
            <div class="shop-tab">
              <p>商品详情</p>
            </div>
            <div class="shop-info">
              <div class="info-details">
                <app-caption title="基本信息"></app-caption>
                <div class="shop-info-body">
                  <ul class="detail_ul">
                    <li
                      class="detail_li clear-float"
                      v-show="serviceDetails.service_name"
                    >
                      <span class="label-li">名称</span>
                      <span class="label-name">
                        {{serviceDetails.service_name}}
                      </span>
                    </li>
                    <li
                      class="detail_li clear-float"
                      v-show="serviceDetails.bar_code"
                    >
                      <span class="label-li">条形码</span>
                      <span class="label-name">
                        {{serviceDetails.bar_code}}
                      </span>
                    </li>
                    <li
                      class="detail_li clear-float"
                      v-show="serviceDetails.ifi_name"
                    >
                      <span class="label-li">分类</span>
                      <span class="label-name">
                        {{serviceDetails.ifi_name}}
                      </span>
                    </li>
                    <li
                      class="detail_li clear-float"
                      v-show="serviceDetails.label_name"
                    >
                      <span class="label-li">标签</span>
                      <span class="label-name">
                        {{serviceDetails.label_name}}
                      </span>
                    </li>
                    <li
                      class="detail_li clear-float"
                      v-show="serviceDetails.price_tag"
                    >
                      <span class="label-li">价格标签</span>
                      <span class="label-name">
                        ￥{{serviceDetails.price_tag}}
                      </span>
                    </li>
                    <!--<li class="detail_li clear-float">-->
                    <!--<span class="label-li">有效时间</span>-->
                    <!--<span class="label-name">精油开背</span>-->
                    <!--</li>-->
                    <!--<li class="detail_li clear-float">-->
                    <!--<span class="label-li">同步发布至</span>-->
                    <!--<span class="label-name">精油开背</span>-->
                    <!--</li>-->
                    <!--<li class="detail_li clear-float">-->
                    <!--<span class="label-li">适用门店</span>-->
                    <!--<span class="label-name">精油开背</span>-->
                    <!--</li>-->
                    <li class="detail_li clear-float">
                      <span class="label-li">网店展示</span>
                      <span class="label-name">
                        <el-tag
                          v-if="serviceDetails"
                          size="medium"
                          :type="serviceDetails.shop_display==1?'':'info'"
                        >
                          {{serviceDetails.shop_display==1?'展示':'不展示'}}
                        </el-tag>
                      </span>
                    </li>
                    <li class="detail_li clear-float">
                      <span class="label-li">状态</span>
                      <span class="label-name">
                        <el-tag
                          v-if="serviceDetails"
                          size="medium"
                          :type="serviceDetails.status==1?'':'danger'"
                        >
                          {{serviceDetails.status==1?'上架中':'已下架'}}
                        </el-tag>
                      </span>
                    </li>
                  </ul>

                  <el-carousel
                    class="shop-bigImg"
                    height="150px"
                    indicator-position="none"
                  >
                    <el-carousel-item
                      v-if="serviceDetails && serviceDetails.imgurl"
                      v-for="(item,index) in serviceDetails.imgurl"
                      style="height: 100%"
                      :key="item"
                    >
                      <img
                        :src="item"
                        onerror="this.src='images/default.jpg'"
                      />
                      <p class="bigIndex">
                        {{index + 1}} / {{serviceDetails.imgurl.length}}
                      </p>
                    </el-carousel-item>
                  </el-carousel>
                  <!--<div class="shop-bigImg"-->
                  <!--v-if="serviceDetails && serviceDetails.imgurl"-->
                  <!--v-for="(item,index) in serviceDetails.imgurl">-->
                  <!--<img :src="item" alt="" onerror="this.src='images/default.jpg'">-->
                  <!--</div>-->
                </div>
                <div>
                  <app-caption title="价格"></app-caption>
                  <el-table
                    v-if="productDetails.sku_attr && productDetails.sku_attr.length>0"
                    class="shopTable"
                    :header-cell-style="headerClass"
                    size="mini"
                    :data="productDetails.sku_attr"
                    style="width: 100%"
                  >
                    <el-table-column prop="sku_name" label="规格">
                      <template slot-scope="scope">
                        <span v-if="scope.row.sku_name">
                          {{scope.row.sku_name}}
                        </span>
                        <span v-if="!scope.row.sku_name">无</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="price" label="售价">
                      <template slot-scope="scope">
                        <span>￥{{scope.row.price}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="duration"
                      label="服务时长(分钟)"
                    ></el-table-column>
                  </el-table>

                  <table
                    class="serverPriceTable"
                    v-else
                    style="text-align: left"
                  >
                    <tr class="PriceTable-tr">
                      <th>规格</th>
                      <th>售价</th>
                      <th>服务时长(分钟)</th>
                    </tr>
                    <tr class="PriceTable-tr">
                      <td>
                        <span v-if="serviceDetails.sku_name">
                          {{serviceDetails.sku_name}}
                        </span>
                        <span v-else="serviceDetails.sku_name">无</span>
                      </td>
                      <td>￥{{serviceDetails.price | filterMoney}}</td>
                      <td>{{serviceDetails.duration}}</td>
                    </tr>
                  </table>

                  <app-caption :title="globalTechnicianName"></app-caption>
                  <el-table
                    class="shopTable"
                    :header-cell-style="headerClass"
                    size="mini"
                    :data="productDetails.staff"
                    style="width: 100%"
                  >
                    <el-table-column
                      prop="nickname"
                      label="姓名"
                    ></el-table-column>
                    <el-table-column
                      prop="group_name"
                      label="分组"
                    ></el-table-column>
                    <el-table-column
                      prop="phone"
                      label="手机号"
                    ></el-table-column>
                  </el-table>
                </div>
              </div>
              <div></div>
            </div>
            <!--以下隐藏  更新了上下架，保留了推广功能-->
            <!--<el-button-group class="btn-group" v-if="shopArr.length>0">-->
            <!--<div class="btn-button obtained" @click="bindChangeFrame" :style="serviceDetails.status==1 ? 'background: #E5484D':'background: #B35DCC' ">-->
            <!--<span v-if="serviceDetails.status!=1">上架</span>-->
            <!--<span v-if="serviceDetails.status!=2">下架</span>-->
            <!--</div>-->
            <!--&lt;!&ndash;第一版没有推广&ndash;&gt;-->
            <!--&lt;!&ndash;<div class="btn-button promotion">推广</div>&ndash;&gt;-->
            <!--&lt;!&ndash;编辑第一版也没有&ndash;&gt;-->
            <!--&lt;!&ndash;<div class="btn-button editBtn" @click="bindEdit(0)">编辑</div>&ndash;&gt;-->
            <!--</el-button-group>-->
            <div class="fixed-btn">
              <el-button-group>
                <div
                  class="btn-button obtained"
                  @click="bindChangeFrame(1)"
                  v-if="serviceDetails.status!=1"
                  :style="serviceDetails.status==1 ? 'background: #E5484D':'background: #3e63dd' "
                >
                  <span>上架</span>
                </div>
                <div
                  class="btn-button obtained"
                  @click="bindChangeFrame(2)"
                  v-if="serviceDetails.status==1"
                  :style="serviceDetails.status==1 ? 'background: #E5484D':'background: #3e63dd' "
                >
                  <span>下架</span>
                </div>
                <!--第一版本不需要发卡-->
                <!--<div class="btn-button editBtn" @click="bindCardIssue">发卡</div>-->
              </el-button-group>
            </div>
          </div>

          <!--卡项上下架-->
          <div v-show="tabCur == 1" class="shop_right height">
            <div class="shop-tab">
              <p>商品详情</p>
            </div>
            <div class="shop-info">
              <div class="info-details">
                <div class="shop-info-body">
                  <div style="width: 50%">
                    <app-caption title="基本信息"></app-caption>
                    <ul class="detail_ul" style="width: 100%">
                      <li class="detail_li clear-float">
                        <span class="label-li">名称</span>
                        <span class="label-name">{{curDetails.card_name}}</span>
                      </li>
                      <li class="detail_li clear-float">
                        <span class="label-li">分类</span>
                        <p class="label-name">
                          <template v-if="curDetails.card_type==1">
                            <span v-if="curDetails.once_cardtype==1">
                              有限次卡
                            </span>
                            <span v-if="curDetails.once_cardtype==2">
                              不限次卡
                            </span>
                            <span v-if="curDetails.once_cardtype==3">通卡</span>
                          </template>
                          <template v-if="curDetails.card_type==2">
                            <span>充值卡</span>
                          </template>
                        </p>
                      </li>
                      <li class="detail_li clear-float">
                        <span class="label-li">售价</span>
                        <span class="label-name">
                          ￥{{curDetails.realPrice}}
                        </span>
                      </li>
                      <li
                        v-if="curDetails.card_type==2"
                        class="detail_li clear-float"
                      >
                        <span class="label-li">充值赠送</span>
                        <span class="label-name">
                          ￥{{curDetails.give | filterMoney}}
                        </span>
                      </li>
                      <li class="detail_li clear-float">
                        <span class="label-li">有效时间</span>
                        <span class="label-name" v-if="curDetails.permanent==2">
                          {{curDetails.validity_time}}
                        </span>
                        <span class="label-name" v-if="curDetails.permanent==1">
                          永久有效
                        </span>
                      </li>
                      <!--<li class="detail_li clear-float">-->
                      <!--<span class="label-li">同步发布至</span>-->
                      <!--<span class="label-name">111</span>-->
                      <!--</li>-->
                      <li class="detail_li clear-float">
                        <span class="label-li">适用门店</span>
                        <span class="label-name">
                          {{curDetails.usestorelist}}
                        </span>
                      </li>
                      <li class="detail_li clear-float">
                        <span class="label-li">网店展示</span>
                        <p class="label-name">
                          <el-tag
                            size="medium"
                            :type="curDetails.sell_online==1?'':'info'"
                          >
                            {{curDetails.sell_online==1?'展示':'不展示'}}
                          </el-tag>
                        </p>
                      </li>
                      <li class="detail_li clear-float">
                        <span class="label-li">状态</span>
                        <p class="label-name">
                          <el-tag
                            size="medium"
                            :type="curDetails.status==1?'':'danger'"
                          >
                            {{curDetails.status==1?'上架中':'已下架'}}
                          </el-tag>
                        </p>
                      </li>
                    </ul>
                  </div>
                  <div style="width: 50%; padding-left: 15px">
                    <app-caption title="适用须知"></app-caption>
                    <pre
                      style="white-space: pre-wrap"
                    ><div class="desc" v-html="curDetails.desc"></div></pre>
                  </div>
                </div>
                <app-caption title="卡项权益"></app-caption>
                <el-table
                  :data="cardBeApplicable"
                  style="width: 100%"
                  :header-cell-style="headerClass"
                  size="mini"
                  show-summary
                  :summary-method="getSummaries"
                >
                  <el-table-column prop="goods_name" label="卡项名称">
                    <template slot-scope="scope">
                      <span v-if="scope.row.isgive==1">
                        {{scope.row.goods_name}}(赠)
                      </span>
                      <span v-if="scope.row.isgive==2">
                        {{scope.row.goods_name}}
                      </span>
                    </template>
                  </el-table-column>

                  <!--  <el-table-column>
                    <template slot="header" slot-scope="scope">
                      <span>卡项折扣</span>
                    </template>
                    <template slot-scope="scope">
                      <span
                        v-if="parseFloat(scope.row.discount)==0 || parseFloat(scope.row.discount)==10"
                      >
                        --
                      </span>
                      <span v-else>{{scope.row.discount}}折</span>
                    </template>
                  </el-table-column> -->
                  <el-table-column prop="num" label="服务次数">
                    <!--isgive 1 赠送  2 不是-->
                    <!--card_type  1 次卡  2 充值卡-->
                    <!--数量（赠送的一定有效，次卡有效（有限次卡有效，无限次卡为-1，通卡可隐藏））数值为-1是表示无限-->
                    <!--次卡类型 1，有限次卡，2，无限次卡，3，通卡-->

                    <template slot-scope="scope">
                      <template v-if="scope.row.card_type==1">
                        <span v-if="scope.row.once_cardtype==1">
                          {{scope.row.num}}次
                        </span>
                        <span v-if="scope.row.once_cardtype==2">
                          <span v-if="scope.row.num==-1">无限次数</span>
                          <span v-else="scope.row.num">
                            {{scope.row.num}}次
                          </span>
                        </span>
                        <span v-if="scope.row.once_cardtype==3">
                          <span v-if="scope.row.isgive==1">
                            {{scope.row.num}}
                          </span>
                          <span v-if="scope.row.isgive==2">--</span>
                        </span>
                      </template>
                      <template v-if="scope.row.card_type==2">
                        <span v-if="scope.row.once_cardtype==1">
                          {{scope.row.num}}次
                        </span>
                        <span v-if="scope.row.once_cardtype==2">
                          {{scope.row.num}}次
                        </span>
                        <span v-if="scope.row.once_cardtype==3">--</span>
                      </template>
                    </template>
                  </el-table-column>
                  <el-table-column prop="validity_time" label="有效时间">
                    <template slot-scope="scope">
                      <span v-if="scope.row.permanent==1">永久有效</span>
                      <span v-if="scope.row.permanent==2">
                        {{scope.row.validity_time}}天
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div class="fixed-btn" v-if="cardArr.length>0">
              <!--<span class="BindVip">此卡已绑定了1位会员</span>-->
              <el-button-group class="btn-group">
                <div
                  class="btn-button obtained"
                  @click="bindChangeFrame(1)"
                  v-if="curDetails.status!=1"
                  :style="curDetails.status==1 ? 'background: #E5484D':'background: #3e63dd' "
                >
                  <span>上架</span>
                </div>
                <div
                  class="btn-button obtained"
                  @click="bindChangeFrame(2)"
                  v-if="curDetails.status!=2"
                  :style="curDetails.status==1 ? 'background: #E5484D':'background: #3e63dd' "
                >
                  <span>下架</span>
                </div>
                <!--第一版本不需要发卡-->
                <!--<div class="btn-button editBtn" @click="bindCardIssue">发卡</div>-->
              </el-button-group>
            </div>
          </div>

          <!--产品上下架-->
          <div v-show="tabCur == 2" class="shop_right height">
            <div class="shop-tab">
              <p>商品详情</p>
            </div>
            <div class="shop-info">
              <div class="info-details">
                <app-caption title="基本信息"></app-caption>
                <div class="shop-info-body">
                  <ul class="detail_ul">
                    <li class="detail_li clear-float">
                      <span class="label-li">名称</span>
                      <span class="label-name">
                        {{productDetailsObj.product_name}}
                      </span>
                    </li>
                    <li class="detail_li clear-float">
                      <span class="label-li">条形码</span>
                      <span class="label-name">
                        {{productDetailsObj.product_barcode}}
                      </span>
                    </li>
                    <li class="detail_li clear-float">
                      <span class="label-li">分类</span>
                      <span class="label-name">
                        {{productDetailsObj.s_class}}
                      </span>
                    </li>
                    <li class="detail_li clear-float">
                      <span class="label-li">标签</span>
                      <span class="label-name">
                        {{productDetailsObj.s_label}}
                      </span>
                    </li>
                    <li class="detail_li clear-float">
                      <span class="label-li">价格标签</span>
                      <span class="label-name">
                        {{productDetailsObj.price_tag}}
                      </span>
                    </li>
                    <!-- <li class="detail_li clear-float">
                      <span class="label-li">总库存</span>
                      <span class="label-name">
                        {{productDetailsObj.totalnum}}
                      </span>
                    </li>
                    <li class="detail_li clear-float">
                      <span class="label-li">统一运费</span>
                      <span class="label-name">
                        ￥{{productDetailsObj.freight | filterMoney}}
                      </span>
                    </li>-->
                    <li class="detail_li clear-float">
                      <span class="label-li">网店展示</span>
                      <p class="label-name">
                        <el-tag
                          size="medium"
                          :type="productDetailsObj.sell_online==1?'':'info'"
                        >
                          {{productDetailsObj.sell_online==1?'展示':'不展示'}}
                        </el-tag>
                      </p>
                    </li>
                    <li class="detail_li clear-float">
                      <span class="label-li">状态</span>
                      <p class="label-name">
                        <el-tag
                          size="medium"
                          :type="productDetailsObj.status=='上架'?'':'danger'"
                        >
                          {{objProduct.status}}
                        </el-tag>
                      </p>
                    </li>
                  </ul>
                  <el-carousel
                    class="shop-bigImg"
                    height="150px"
                    indicator-position="none"
                  >
                    <el-carousel-item
                      v-if="productDetailsObj && productDetailsObj.imgarr"
                      v-for="(item,index) in productDetailsObj.imgarr"
                      style="height: 100%"
                      :key="item"
                    >
                      <img
                        :src="item"
                        onerror="this.src='images/default.jpg'"
                      />
                      <p class="bigIndex">
                        {{index + 1}} / {{serviceDetails.imgurl.length}}
                      </p>
                    </el-carousel-item>
                  </el-carousel>
                </div>
                <app-caption title="价格/库存"></app-caption>

                <el-table
                  v-if="productPriceTabel.length>0"
                  :data="productPriceTabel"
                  style="width: 100%"
                  :header-cell-style="headerClass"
                  size="mini"
                >
                  <el-table-column prop="sku" label="规格">
                    <template slot-scope="scope">
                      <span v-if="scope.row.sku">{{scope.row.sku}}</span>
                      <span v-if="!scope.row.sku">无</span>
                    </template>
                  </el-table-column>
                  <!--<el-table-column prop="cost_price" label="成本价">-->
                  <!--<template slot-scope="scope">-->
                  <!--<span>{{scope.row.cost_price/100}}</span>-->
                  <!--</template>-->
                  <!--</el-table-column>-->
                  <el-table-column prop="price" label="售价">
                    <template slot-scope="scope">
                      <span>￥{{scope.row.price/100}}</span>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column
                    prop="totalnum"
                    label="库存"
                  ></el-table-column> -->
                  <el-table-column
                    prop="totalsell"
                    label="销量"
                  ></el-table-column>
                  <el-table-column
                    prop="barcode"
                    label="条形码"
                  ></el-table-column>
                </el-table>

                <table v-else class="serverPriceTable" style="text-align: left">
                  <tr class="PriceTable-tr">
                    <th>规格</th>
                    <th>售价</th>
                    <!-- <th>库存</th> -->
                    <th>销量</th>
                  </tr>
                  <tr class="PriceTable-tr">
                    <td>无</td>
                    <td>{{productDetailsObj.s_price}}</td>
                    <!-- <td>{{productDetailsObj.totalnum}}</td> -->
                    <td>{{productDetailsObj.totalsell}}</td>
                  </tr>
                </table>
              </div>
            </div>
            <div class="fixed-btn">
              <!--<span class="BindVip">此卡已绑定了1位会员</span>-->
              <!--第一版没有编辑-->
              <!--<div class="btn-button editBtn">编辑</div>-->
              <el-button-group class="btn-group">
                <div
                  class="btn-button obtained"
                  @click="bindChangeFrame(1)"
                  v-if="objProduct.status=='未上架'"
                  :style="productDetailsObj.statusId==1 ? 'background: #E5484D':'background: #3e63dd' "
                >
                  <span>上架</span>
                </div>
                <div
                  class="btn-button obtained"
                  @click="bindChangeFrame(2)"
                  v-if="objProduct.status=='上架中'"
                  :style="productDetailsObj.statusId==1 ? 'background: #E5484D':'background: #3e63dd' "
                >
                  <span>下架</span>
                </div>
                <!--第一版本不需要发卡-->
                <!--<div class="btn-button editBtn" @click="bindCardIssue">发卡</div>-->
              </el-button-group>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-dialog title="编辑产品" :visible.sync="isEdit" width="500px">
        <div class="edit_body">
          <ul class="edit-wrap">
            <li class="edit-li">
              <label class="edit-li-label">名称</label>
              <div class="edit-box">
                <el-input v-model="curEdit.service_name"></el-input>
              </div>
            </li>
            <li class="edit-li" style="display: none">
              <label class="edit-li-label">名称</label>
              <div class="edit-box">
                <el-upload
                  action="###"
                  list-type="picture-card"
                  :on-preview="handlePictureCardPreview"
                  :on-progress="handleProgress"
                  :on-remove="handleRemove"
                  :limit="5"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>
              </div>
            </li>
            <li class="tip" style="display: none">
              <p>最多上传5张，建议尺寸640*640px</p>
            </li>
            <li class="edit-li">
              <label class="edit-li-label">售价</label>
              <div class="edit-box">
                <el-input v-model="curEdit.price"></el-input>
              </div>
            </li>
          </ul>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel" @click="isEdit = false">取 消</el-button>
          <el-button type="primary" @click="subEdit">确 定</el-button>
        </span>
      </el-dialog>
      <!--编辑--预览-->
      <el-dialog :visible.sync="isPreview" width="300px" top="5vh">
        <img width="100%" :src="previewImageUrl" alt="" />
      </el-dialog>

      <!--发卡-->
      <el-dialog
        class="cardIssue-mask"
        title="发卡"
        :visible.sync="isCardIssue"
      >
        <div>
          <!--<div class="cardIssueTitle">-->
          <!--<i class="el-icon-arrow-left"></i>-->
          <!--<p class="cardIssue-text">商品详情</p>-->
          <!--<el-button type="primary" size="mini">保存二维码到相册</el-button>-->
          <!--</div>-->
        </div>
        <div class="cardIssue-main">
          <p>{{curDetails.card_name}}</p>
          <p>扫一扫，领取二维码</p>
          <img src="" alt="" />
        </div>
        <div class="cardIssue-table">
          <app-caption title="服务权益"></app-caption>
          <el-table :data="tableData" style="width: 100%">
            <el-table-column
              prop="date"
              label="日期"
              width="180"
            ></el-table-column>
            <el-table-column
              prop="name"
              label="姓名"
              width="180"
            ></el-table-column>
            <el-table-column prop="address" label="地址"></el-table-column>
          </el-table>
        </div>
      </el-dialog>

      <!--筛选使用-->
      <div
        v-if="filterTabel.isType || filterTabel.isStatus || filterTabel.isSort"
        class="popover-mask"
      ></div>
    </div>
  </body>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/vue-infinite-scroll.js"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <script src="js/unocss.theme.js"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <script src="./component/components.js"></script>
  <script src="js/shop_index.js"></script>
</html>
